## Project Overview

This is a Ruby on Rails and Vue.js application called "Týmbox" for workforce management. It features a modern, API-first architecture with JWT-based authentication, multi-tenancy, and real-time updates via Action Cable. The frontend is a Single-Page Application (SPA) built with Vue.js, Vuex, and Vue Router, using Vite for asset bundling. The application also includes a server-rendered admin interface and some legacy pages with individually mounted Vue components.

### Key Technologies

*   **Backend:** Ruby on Rails 7, PostgreSQL, Redis, Sidekiq, Action Cable
*   **Frontend:** Vue.js 3, Vuex, Vue Router, Axios, Vite, Tailwind CSS
*   **Authentication:** JWT-only with HttpOnly cookies for refresh tokens
*   **Architecture:** Multi-tenant, API-first, hybrid SPA and server-rendered views

## Building and Running

### Prerequisites

*   Ruby 3.1.2
*   Node.js 16+
*   PostgreSQL 12+
*   Redis 6+

### Installation

1.  **Install dependencies:**
    ```bash
    bundle install
    npm install
    ```

2.  **Set up the database and environment:**
    ```bash
    cp .env.example .env
    rails db:create db:migrate db:seed
    rails credentials:edit
    ```

### Running the Application

*   **Start all services (<PERSON>s, Vite, Redis):**
    ```bash
    foreman start -f Procfile.dev
    ```

*   **Access the application:**
    *   Web: `http://localhost:5100`
    *   API: `http://localhost:5100/api/v1`

### Testing

*   **Run all tests:**
    ```bash
    bin/rails test
    npm test
    npm run test:e2e
    ```

## Development Conventions

*   **Authentication:** All new features should use the JWT authentication system.
*   **Multi-Tenancy:** Maintain data isolation between tenants (companies).
*   **Code Style:**
    *   **Ruby:** Follow Rails conventions and use RuboCop.
    *   **JavaScript:** Use ES6+ and Prettier for formatting.
    *   **Vue:** Use the Options API and single-file components.
*   **API:** The API is versioned under `/api/v1`.
*   **Frontend:** The main Vue.js application is located in `app/frontend`, with the entry point at `app/frontend/entrypoints/application.js`. The application uses a hybrid approach, with a full SPA for the main user interface and legacy pages with individually mounted Vue components.
