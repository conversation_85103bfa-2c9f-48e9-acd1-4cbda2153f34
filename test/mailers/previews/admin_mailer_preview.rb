# ABOUTME: Preview class for AdminMailer emails
# ABOUTME: Allows viewing subscription order emails in development mode

# Preview this email at http://************:5100/rails/mailers/admin_mailer/subscription_order
# Template at: /app/views/admin_mailer/subscription_order.html.erb

class AdminMailerPreview < ActionMailer::Preview
  def subscription_order
    subscription_order = OpenStruct.new(
      tier: 'premium',
      company_name: 'Test Company s.r.o.',
      company_id: '12345678',
      company_address: 'Testovací 123, 110 00 Praha 1',
      email: '<EMAIL>',  # Email from form (for invoicing)
      billing_period: '12',
      custom_message: 'Potřebujeme fakturu na jiné <PERSON>, prosíme o kontakt.'
    )
    
    # Mock current user who initiated the order
    current_user = OpenStruct.new(
      name: '<PERSON>',
      email: '<EMAIL>'  # User's actual login email
    )
    
    AdminMailer.subscription_order(subscription_order, current_user)
  end
end