# ABOUTME: Simple test to verify validation error handling works
# ABOUTME: Tests the basic error display mechanism for company forms

require 'rails_helper'

RSpec.describe 'Simple company validation', type: :feature, js: true do
  it 'displays validation errors for empty company name' do
    # Use existing user and authenticate
    visit '/login'
    fill_in 'user[email]', with: '<EMAIL>'
    fill_in 'user[password]', with: '123456'
    click_button 'Přihlásit se'
    
    # Wait for redirect
    expect(page).to have_current_path('/dashboard', wait: 10)
    
    # Navigate to companies page
    visit '/companies'
    
    # Wait for page to load
    expect(page).to have_text('Pracovní prostory', wait: 10)
    
    # Try to clear the name field if there's an existing company
    if page.has_field?('company_name', wait: 2)
      name_input = find('#company_name')
      
      # Clear the field
      3.times { name_input.send_keys [:control, 'a'] }
      name_input.send_keys :delete
      
      # Try to submit with empty name
      within('form') do
        click_button 'Uložit'
      end
      
      # We should see either a validation error or the required field message
      # HTML5 validation will prevent submission with required field
      # Or we'll get a backend validation error
      expect(page).to satisfy("have validation feedback") do |page|
        page.has_css?('.text-red-600') || # Our custom error display
        page.has_css?('#company_name:invalid') || # HTML5 validation state
        page.has_css?('#company_name.border-red-500') # Error styling
      end
    else
      skip "No editable company form found"
    end
  end
end