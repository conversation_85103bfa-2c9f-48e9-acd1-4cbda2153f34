# ABOUTME: Tests for email confirmation flow fixes addressing TYM-116 issues
# ABOUTME: Validates company creation, resend logic, error handling, and translations

require 'rails_helper'

RSpec.describe 'Email Confirmation Flow', type: :feature, js: true do
  describe 'New user registration and confirmation' do
    let(:email) { '<EMAIL>' }
    let(:password) { 'SecurePassword123!' }
    
    context 'when registering a new user' do
      it 'creates a default company for the user' do
        # Register new user
        user = User.create!(
          email: email,
          password: password,
          password_confirmation: password
        )
        
        # Simulate registration controller logic
        AuthController.new.send(:create_default_company_for_user, user)
        
        # Verify user has a company
        expect(user.reload.primary_company).to be_present
        expect(user.primary_company.name).to eq("Newuser's Company")
        expect(user.company_user_roles.first.role.name).to eq('owner')
      end
    end
    
    context 'when confirming email' do
      let(:user) { User.create!(email: email, password: password) }
      let(:token) { SecureRandom.hex(20) }
      
      before do
        # Setup confirmation token in Redis
        RedisService.set(:email_confirmation, 
          [user.id], 
          { token: token, expires_at: 1.hour.from_now.to_i }
        )
        RedisService.set(:confirmation_token, 
          [token], 
          { user_id: user.id }
        )
      end
      
      it 'successfully confirms email and logs user in with company context' do
        # User model callbacks should have already created the company
        # The company should have Czech naming format (titleized with spaces)
        expect(user.primary_company).to be_present
        expect(user.primary_company.name).to match(/^[A-Z]\w+ [A-Z]\w+ Firma \w+$/)
        
        # Visit confirmation page
        visit "/cs/confirm-email?token=#{token}"
        
        # Should see success message
        expect(page).to have_content('Operace byla úspěšná')
        
        # Should be redirected to dashboard
        expect(page).to have_current_path('/cs/dashboard', wait: 5)
      end
      
      it 'handles invalid tokens with helpful error message' do
        visit "/cs/confirm-email?token=invalid_token_123"
        
        # Should see error with suggestion
        expect(page).to have_content('Neplatná nebo vypršená autentifikace')
        expect(page).to have_content('Tento potvrzovací odkaz je neplatný nebo vypršel')
        
        # Should show resend form
        expect(page).to have_field('email')
        expect(page).to have_button('Znovu odeslat potvrzení')
      end
    end
    
    context 'when resending confirmation' do
      let(:user) { User.create!(email: email, password: password) }
      
      it 'provides proper feedback for already confirmed users' do
        # Mark user as confirmed
        user.update!(confirmed_at: Time.current)
        
        visit '/cs/users/confirmation/new'
        
        fill_in 'email', with: email
        click_button 'Znovu odeslat potvrzení'
        
        # Should see already confirmed message
        expect(page).to have_content('Tento e-mail již byl potvrzen')
        
        # Should redirect to login
        expect(page).to have_current_path('/cs/login', wait: 5)
      end
      
      it 'sends confirmation for unconfirmed users' do
        visit '/cs/users/confirmation/new'
        
        fill_in 'email', with: email
        click_button 'Znovu odeslat potvrzení'
        
        # Should see success message
        expect(page).to have_content('Pokud je tento e-mail registrován')
      end
    end
  end
end