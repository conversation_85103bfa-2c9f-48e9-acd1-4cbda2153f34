# ABOUTME: Tests validation error handling for inline company editing
# ABOUTME: Verifies proper display of field-specific validation errors

require 'rails_helper'

RSpec.describe 'Company inline editing validation', type: :feature, js: true do
  let(:owner) { create(:user, email: '<EMAIL>', password: '123456') }
  let(:company) { create(:company, name: 'Test Company') }
  let(:owner_role) { create(:role, name: 'owner') }
  let!(:company_user_role) { create(:company_user_role, user: owner, company: company, role: owner_role) }
  
  before do
    # Authenticate via API
    page.driver.browser.set_extra_headers('Accept' => 'application/json')
    
    # Login to get session
    visit '/login'
    fill_in 'user[email]', with: '<EMAIL>'
    fill_in 'user[password]', with: '123456'
    click_button 'Přihlásit se'
    
    # Wait for redirect
    expect(page).to have_current_path('/dashboard', wait: 5)
  end

  it 'displays validation errors for invalid company name' do
    # Create another company with a name we'll try to duplicate
    other_company = create(:company, name: 'Existing Company')
    
    # Navigate to companies page
    visit '/companies'
    
    # Wait for company data to load
    expect(page).to have_text('Test Company', wait: 5)
    
    # Clear and enter duplicate name
    name_input = find('#company_name')
    name_input.fill_in with: ''
    name_input.fill_in with: 'Existing Company'
    
    # Submit the form
    within('form') do
      click_button 'Uložit'
    end
    
    # Should see validation error next to the name field
    expect(page).to have_css('.text-red-600', text: /již existuje|already exists/i, wait: 5)
    
    # The form should still be visible (not navigated away)
    expect(page).to have_field('company_name')
    
    # The input should have error styling
    expect(page).to have_css('#company_name.border-red-500')
  end
  
  it 'displays validation errors for invalid settings' do
    visit '/companies'
    
    # Wait for settings form to load
    expect(page).to have_text('Nastavení', wait: 5)
    
    # Enter invalid break duration (negative number)
    break_input = find('#break_duration')
    break_input.fill_in with: ''
    break_input.fill_in with: '-10'
    
    # Submit the settings form
    within('form', text: 'Délka přestávky') do
      click_button 'Aktualizovat'
    end
    
    # Should see validation error
    expect(page).to have_css('.text-red-600', text: /musí být větší|must be greater/i, wait: 5)
    
    # The input should have error styling
    expect(page).to have_css('#break_duration.border-red-500')
  end
  
  it 'clears errors when switching between companies' do
    # Create second company
    company2 = create(:company, name: 'Second Company')
    create(:company_user_role, user: owner, company: company2, role: owner_role)
    
    visit '/companies'
    
    # Trigger a validation error on first company
    name_input = find('#company_name')
    name_input.fill_in with: ''
    
    within('form') do
      click_button 'Uložit'
    end
    
    # Should see validation error
    expect(page).to have_css('.text-red-600', wait: 5)
    
    # Switch to second company
    click_button 'Second Company'
    
    # Errors should be cleared
    expect(page).not_to have_css('.text-red-600')
    expect(page).not_to have_css('.border-red-500')
  end
end