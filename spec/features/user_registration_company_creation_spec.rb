# ABOUTME: Tests user registration flow with automatic Czech company creation
# ABOUTME: Verifies TYM-116 fix that removes duplicate company generation

require 'rails_helper'

RSpec.describe 'User Registration and Company Creation', type: :feature, js: true do
  describe 'new user registration' do
    let(:email) { '<EMAIL>' }
    let(:password) { 'SecurePassword123!' }
    
    context 'via API endpoint' do
      it 'creates user with Czech-named company automatically' do
        # Register new user via API
        post '/api/v1/auth/jwt_register', params: {
          user: {
            email: email,
            password: password,
            password_confirmation: password
          }
        }
        
        expect(response).to have_http_status(:created)
        
        # Find the created user
        user = User.find_by(email: email)
        expect(user).to be_present
        
        # Verify user profile was created
        expect(user.user_profile).to be_present
        
        # Verify user settings were created
        expect(user.user_setting).to be_present
        
        # Verify company was created with Czech naming format
        expect(user.primary_company).to be_present
        company = user.primary_company
        
        # Company name should match Czech format: "adjective-quality-firma-hash"
        expect(company.name).to match(/^[A-Z]\w+-[A-Z]\w+-Firma-\w+$/)
        
        # Verify company attributes
        expect(company.is_personal).to be true
        expect(company.slug).to be_present
        expect(company.subdomain).to be_present
        
        # Verify user has owner role
        role = user.role_in(company)
        expect(role).to be_present
        expect(role.name).to eq('owner')
        
        # Verify company_user_role is properly configured
        company_user_role = user.company_user_roles.find_by(company: company)
        expect(company_user_role.is_primary).to be true
        expect(company_user_role.active).to be true
        
        # Verify contract was created
        contract = company.contracts.find_by(user: user)
        expect(contract).to be_present
        expect(contract.email).to eq(email)
      end
      
      it 'does not create duplicate companies' do
        # Register user
        post '/api/v1/auth/jwt_register', params: {
          user: {
            email: email,
            password: password,
            password_confirmation: password
          }
        }
        
        user = User.find_by(email: email)
        initial_company_count = user.companies.count
        
        # Verify only one company was created
        expect(initial_company_count).to eq(1)
        
        # Try to trigger any duplicate creation paths
        user.reload
        
        # Company count should remain the same
        expect(user.companies.count).to eq(initial_company_count)
      end
      
      it 'handles callback failures gracefully' do
        # Temporarily break company creation
        allow_any_instance_of(User).to receive(:create_personal_workspace).and_raise(StandardError, 'Test error')
        
        # Registration should still succeed even if company creation fails
        post '/api/v1/auth/jwt_register', params: {
          user: {
            email: '<EMAIL>',
            password: password,
            password_confirmation: password
          }
        }
        
        # User should be created even if company creation failed
        user = User.find_by(email: '<EMAIL>')
        expect(user).to be_present
      end
    end
    
    context 'email confirmation flow' do
      let(:user) { User.create!(email: email, password: password) }
      let(:token) { 'test_confirmation_token_123' }
      
      before do
        # Set up confirmation token in Redis
        RedisService.set(:confirmation_token, 
          [token], 
          { user_id: user.id }
        )
      end
      
      it 'user has company context after email confirmation' do
        # User should already have a company from callbacks
        expect(user.primary_company).to be_present
        
        # Visit confirmation page
        visit "/cs/confirm-email?token=#{token}"
        
        # Should be able to access dashboard with company context
        expect(page).to have_current_path('/cs/dashboard', wait: 5)
        
        # Verify API calls work with company context
        # (X-Company-ID header should be set automatically)
        visit '/cs/dashboard'
        expect(page).not_to have_content('Please provide X-Company-ID header')
      end
    end
    
    context 'callback ordering verification' do
      it 'creates resources in correct order' do
        creation_order = []
        
        # Mock methods to track calling order
        allow_any_instance_of(User).to receive(:create_user_profile) do |user|
          creation_order << :profile
          user.build_user_profile.save
        end
        
        allow_any_instance_of(User).to receive(:create_default_settings) do |user|
          creation_order << :settings
          user.build_user_setting.save
        end
        
        allow_any_instance_of(User).to receive(:setup_personal_workspace) do |user|
          creation_order << :workspace
          user.send(:create_personal_workspace)
        end
        
        # Create user
        User.create!(email: '<EMAIL>', password: password)
        
        # Verify correct order: profile, settings, then workspace
        expect(creation_order).to eq([:profile, :settings, :workspace])
      end
    end
  end
end