# ABOUTME: Tests user registration API with automatic Czech company creation
# ABOUTME: Verifies TYM-116 fix that removes duplicate company generation

require 'rails_helper'

RSpec.describe 'User Registration and Company Creation', type: :request do
  describe 'POST /api/v1/auth/jwt_register' do
    let(:email) { '<EMAIL>' }
    let(:password) { 'SecurePassword123!' }
    let(:valid_params) do
      {
        user: {
          email: email,
          password: password,
          password_confirmation: password
        }
      }
    end
    
    context 'successful registration' do
      it 'creates user with Czech-named company automatically' do
        # Register new user via API
        post '/api/v1/auth/jwt_register', params: valid_params
        
        expect(response).to have_http_status(:created)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        
        # Find the created user
        user = User.find_by(email: email)
        expect(user).to be_present
        
        # Verify user profile was created
        expect(user.user_profile).to be_present
        
        # Verify user settings were created
        expect(user.user_setting).to be_present
        
        # Verify company was created with Czech naming format
        expect(user.primary_company).to be_present
        company = user.primary_company
        
        # Company name should match Czech format after titleize
        # Format is like "Skvele Chytra Firma A7x9" (spaces, not hyphens)
        expect(company.name).to match(/^[A-Z]\w+ [A-Z]\w+ Firma \w+$/)
        
        # Verify company attributes
        expect(company.is_personal).to be true
        expect(company.slug).to be_present
        expect(company.subdomain).to be_present
        
        # Verify user has owner role
        role = user.role_in(company)
        expect(role).to be_present
        expect(role.name).to eq('owner')
        
        # Verify company_user_role is properly configured
        company_user_role = user.company_user_roles.find_by(company: company)
        expect(company_user_role.is_primary).to be true
        expect(company_user_role.active).to be true
        
        # Verify contract was created
        contract = company.contracts.find_by(user: user)
        expect(contract).to be_present
        expect(contract.email).to eq(email)
      end
      
      it 'does not create duplicate companies' do
        # Register user
        post '/api/v1/auth/jwt_register', params: valid_params
        expect(response).to have_http_status(:created)
        
        user = User.find_by(email: email)
        initial_company_count = user.companies.count
        
        # Verify only one company was created
        expect(initial_company_count).to eq(1)
        
        # Reload to ensure no additional callbacks fire
        user.reload
        
        # Company count should remain the same
        expect(user.companies.count).to eq(initial_company_count)
      end
      
      it 'creates company with correct Czech naming pattern' do
        post '/api/v1/auth/jwt_register', params: valid_params
        
        user = User.find_by(email: email)
        company = user.primary_company
        
        # Verify the Czech naming components
        name_parts = company.slug.split('-')
        expect(name_parts.length).to eq(4)
        
        # Check that components match expected word lists
        compound_adjectives = %w[
          skvele uzasne nezastavitelne vyjimecne nezapomenutelne 
          prekvapive neporazitelne fantasticky neprekonatelne 
          neuveritelne jedinecne uspesne nadejne
        ]
        
        qualities = %w[
          chytra rychla moudra pevna silna uspesna odvazna 
          aktivni ambiciozni tvoriva bystra zdatna pracovita 
          schopna nadana ustretova spolehliva inovativni odhodlana 
          vytrvala organizovana dusledna precizni zodpovedna pratelska 
          otevrena progresivni kompetentni efektivni duveryhodna 
          kreativni
        ]
        
        expect(compound_adjectives).to include(name_parts[0])
        expect(qualities).to include(name_parts[1])
        expect(name_parts[2]).to eq('firma')
        expect(name_parts[3]).to match(/^[a-z0-9]+$/) # hash component
      end
    end
    
    context 'callback ordering verification' do
      it 'creates resources in correct order' do
        # We can't easily test the exact order in a request spec,
        # but we can verify all resources are created properly
        post '/api/v1/auth/jwt_register', params: valid_params
        
        user = User.find_by(email: email)
        
        # All resources should exist
        expect(user.user_profile).to be_present
        expect(user.user_setting).to be_present
        expect(user.primary_company).to be_present
        
        # Company's contract should reference the user profile data
        contract = user.primary_company.contracts.first
        # If profile was created first (correct), contract will have proper data
        # If workspace was created first (wrong), contract would use email as first_name
        expect(contract.first_name).to eq(user.user_profile.first_name || email)
      end
    end
    
    context 'error handling' do
      it 'returns appropriate error for duplicate email' do
        # Create first user
        User.create!(email: email, password: password)
        
        # Try to register with same email
        post '/api/v1/auth/jwt_register', params: valid_params
        
        expect(response).to have_http_status(:unprocessable_entity)
        json = JSON.parse(response.body)
        expect(json['success']).to be false
        expect(json['errors']).to be_present
      end
    end
  end
end