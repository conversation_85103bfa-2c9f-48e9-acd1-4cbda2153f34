# ABOUTME: Tests email confirmation API endpoint with Czech company creation
# ABOUTME: Verifies proper company context is set after confirmation

require 'rails_helper'

RSpec.describe 'Email Confirmation API', type: :request do
  describe 'POST /api/v1/auth/confirm_email' do
    let(:user) { User.create!(email: '<EMAIL>', password: 'password123') }
    let(:token) { 'test_confirmation_token' }
    
    before do
      # Set up confirmation token in Redis (both forward and reverse lookup)
      # Forward lookup: user_id -> token data
      RedisService.set(:email_confirmation, 
        [user.id], 
        { 
          token: token,
          expires_at: 24.hours.from_now.to_i 
        }
      )
      
      # Reverse lookup: token -> user_id
      RedisService.set(:confirmation_token, 
        [token], 
        { user_id: user.id }
      )
    end
    
    it 'confirms email and returns user with Czech company context' do
      # Verify user has company from callbacks
      expect(user.primary_company).to be_present
      expect(user.primary_company.name).to match(/^[A-Z]\w+ [A-Z]\w+ Firma \w+$/)
      
      # Confirm email via API
      post '/api/v1/auth/confirm_email', params: { confirmation_token: token }
      
      if response.status != 200
        puts "Error response: #{response.body}"
      end
      
      expect(response).to have_http_status(:ok)
      json = JSON.parse(response.body)
      
      # Should return success with JWT tokens
      expect(json['success']).to be true
      expect(json['access_token']).to be_present
      expect(json['user']).to be_present
      
      # Should include company information
      expect(json['user']['company_id']).to eq(user.primary_company.id)
      expect(json['user']['companies']).to be_present
      expect(json['user']['companies'].first['name']).to match(/^[A-Z]\w+ [A-Z]\w+ Firma \w+$/)
      expect(json['user']['companies'].first['role']).to eq('owner')
      expect(json['user']['companies'].first['is_primary']).to be true
      
      # User should be confirmed
      user.reload
      expect(user.confirmed_at).to be_present
    end
    
    it 'handles invalid token appropriately' do
      post '/api/v1/auth/confirm_email', params: { confirmation_token: 'invalid_token' }
      
      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      
      expect(json['error']).to be_present
      expect(json['error_type']).to eq('invalid_token')
      expect(json['suggestion']).to be_present
    end
    
    it 'handles missing token appropriately' do
      post '/api/v1/auth/confirm_email', params: {}
      
      expect(response).to have_http_status(:unprocessable_entity)
      json = JSON.parse(response.body)
      
      expect(json['error']).to be_present
      expect(json['error_type']).to eq('missing_token')
    end
  end
  
  describe 'POST /api/v1/auth/resend_confirmation' do
    context 'with unconfirmed user' do
      let(:user) { User.create!(email: '<EMAIL>', password: 'password123') }
      
      it 'sends confirmation email' do
        post '/api/v1/auth/resend_confirmation', params: { email: user.email }
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        
        expect(json['success']).to be true
        expect(json['already_confirmed']).to be_falsey
        expect(json['message']).to be_present
      end
    end
    
    context 'with already confirmed user' do
      let(:user) do
        u = User.create!(email: '<EMAIL>', password: 'password123')
        u.update!(confirmed_at: Time.current)
        u
      end
      
      it 'returns already confirmed message' do
        post '/api/v1/auth/resend_confirmation', params: { email: user.email }
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        
        expect(json['success']).to be true
        expect(json['already_confirmed']).to be true
        # Message is in Czech: "Tento e-mail již byl potvrzen"
        expect(json['message']).to include('již byl potvrzen')
      end
    end
    
    context 'with non-existent email' do
      it 'returns generic success for security' do
        post '/api/v1/auth/resend_confirmation', params: { email: '<EMAIL>' }
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        
        # Should return same success message as valid emails (anti-enumeration)
        expect(json['success']).to be true
        expect(json['message']).to be_present
      end
    end
  end
end