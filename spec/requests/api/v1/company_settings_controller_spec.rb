# ABOUTME: Test for company settings API including cross-company access
# ABOUTME: Verifies authorization and fetching settings for different companies

require 'rails_helper'

RSpec.describe Api::V1::CompanySettingsController, type: :request do
  let(:owner_email) { "<EMAIL>" }
  let(:owner_password) { "123456" }
  
  describe "GET /api/v1/companies/:id/settings" do
    it "allows owner to fetch settings for their companies" do
      # Login as owner
      post '/api/v1/auth/jwt_login', params: { 
        email: owner_email, 
        password: owner_password 
      }, headers: { 'Accept' => 'application/json' }
      expect(response).to have_http_status(:ok)
      
      # Get JWT token from response
      jwt_token = JSON.parse(response.body)['token']
      
      # Get list of companies
      get '/api/v1/companies', headers: { 
        'Authorization' => "Bearer #{jwt_token}",
        'Accept' => 'application/json'
      }
      companies = JSON.parse(response.body)['companies']
      
      # Try to fetch settings for each company
      companies.each do |company|
        get "/api/v1/companies/#{company['id']}/settings", headers: {
          'Authorization' => "Bearer #{jwt_token}",
          'Accept' => 'application/json'
        }
        
        # Owner should have access to all their companies
        expect(response).to have_http_status(:ok)
        settings = JSON.parse(response.body)['company_setting']
        expect(settings).to have_key('break_duration')
        expect(settings).to have_key('approve_vacations')
      end
    end
    
    it "returns 403 for companies user doesn't have access to" do
      # Create a separate company that the user doesn't belong to
      other_company = Company.create!(name: "Other Company", address: "123 Street")
      
      # Login as owner
      post '/api/v1/auth/jwt_login', params: { 
        email: owner_email, 
        password: owner_password 
      }, headers: { 'Accept' => 'application/json' }
      jwt_token = JSON.parse(response.body)['token']
      
      # Try to fetch settings for company user doesn't belong to
      get "/api/v1/companies/#{other_company.id}/settings", headers: {
        'Authorization' => "Bearer #{jwt_token}",
        'Accept' => 'application/json'
      }
      
      expect(response).to have_http_status(:forbidden)
      error = JSON.parse(response.body)
      expect(error['error']).to include('Not authorized')
    end
    
    it "returns 404 for non-existent company" do
      # Login as owner
      post '/api/v1/auth/jwt_login', params: { 
        email: owner_email, 
        password: owner_password 
      }, headers: { 'Accept' => 'application/json' }
      jwt_token = JSON.parse(response.body)['token']
      
      # Try to fetch settings for non-existent company
      get "/api/v1/companies/999999/settings", headers: {
        'Authorization' => "Bearer #{jwt_token}",
        'Accept' => 'application/json'
      }
      
      expect(response).to have_http_status(:not_found)
      error = JSON.parse(response.body)
      expect(error['error']).to include('not found')
    end
  end
end