# ABOUTME: Tests for proper redirection and message handling in resend confirmation flow
# ABOUTME: Validates fixes for TYM-116 including locale handling and sessionStorage messages

require 'rails_helper'

RSpec.describe 'Resend Confirmation Redirect', type: :request do
  describe 'POST /api/v1/auth/resend_confirmation' do
    let(:email) { '<EMAIL>' }
    let(:password) { 'SecurePassword123!' }
    
    context 'when user is already confirmed' do
      let!(:user) do
        User.create!(
          email: email,
          password: password,
          confirmed_at: Time.current
        )
      end
      
      it 'returns already_confirmed flag in response' do
        post '/api/v1/auth/resend_confirmation', params: { email: email }
        
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['already_confirmed']).to be true
        # Check for Czech message about already confirmed
        expect(json_response['message']).to match(/již byl potvrzen|already been confirmed/i)
      end
    end
    
    context 'when user is not confirmed' do
      let!(:user) do
        User.create!(
          email: email,
          password: password,
          confirmed_at: nil
        )
      end
      
      it 'sends confirmation email without already_confirmed flag' do
        post '/api/v1/auth/resend_confirmation', params: { email: email }
        
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['already_confirmed']).to be_nil
        # Check for Czech message about email sent
        expect(json_response['message']).to match(/Potvrzovací e-mail byl odeslán|will receive a confirmation email/i)
      end
    end
    
    context 'when email does not exist' do
      it 'returns success for security reasons' do
        post '/api/v1/auth/resend_confirmation', params: { email: '<EMAIL>' }
        
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        # Same message as for valid emails for security
        expect(json_response['message']).to match(/Potvrzovací e-mail byl odeslán|will receive a confirmation email/i)
      end
    end
  end
end