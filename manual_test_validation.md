# Manual Test for Company Validation Error Display

## Test Steps:

1. Login to the application as an owner:
   - Email: <EMAIL>
   - Password: 123456

2. Navigate to Companies page (/companies)

3. Test duplicate name validation:
   - Note the current company name
   - Try to change it to a name that already exists (e.g., another company's name)
   - Click "Uložit" (Save)
   
## Expected Results:

- ✅ A validation error message should appear in the flash message
- ✅ The specific field (name) should show an error message below it
- ✅ The input field should have a red border
- ✅ The error message should say something like "has already been taken" or the Czech equivalent

## Test break_duration validation:

1. In the Settings section, enter an invalid break duration:
   - Enter a negative number (e.g., -10)
   - Click "Aktualizovat" (Update)

## Expected Results:

- ✅ A validation error message should appear
- ✅ The break_duration field should show an error below it
- ✅ The input field should have a red border

## Browser Console Commands:

To check the current Vue component state:
```javascript
// Get the Vue component instance
const app = document.querySelector('#app').__vue_app__;
const component = app._context.components.CompanyIndex;

// Check errors object
console.log('Current errors:', component.errors);
```

## Debug Information:

The backend now returns errors in this format:
```json
{
  "success": false,
  "errors": {
    "name": ["has already been taken"],
    "field2": ["error message"]
  },
  "message": "Name has already been taken"
}
```

The frontend displays:
1. The `message` in the flash notification
2. Field-specific errors from the `errors` object next to each field