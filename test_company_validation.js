// ABOUTME: Script to test company name validation errors
// ABOUTME: Tests that duplicate company names show field-specific errors

// This script tests the validation error display by:
// 1. Creating a company with a known name
// 2. Trying to update another company with the same name
// 3. Verifying that errors appear next to the form field

async function testCompanyValidation() {
  console.log('Testing company name validation...');
  
  // Try updating the current company with a duplicate name
  const nameInput = document.querySelector('#company_name');
  if (!nameInput) {
    console.error('Company name input not found');
    return;
  }
  
  // Store the original value
  const originalName = nameInput.value;
  console.log('Original name:', originalName);
  
  // Try to set a duplicate name (assuming there's a company named "Test Company")
  nameInput.value = 'Test Company';
  nameInput.dispatchEvent(new Event('input', { bubbles: true }));
  
  // Submit the form
  const saveButton = document.querySelector('form button[type="submit"]');
  if (saveButton) {
    console.log('Clicking save button...');
    saveButton.click();
    
    // Wait for response
    setTimeout(() => {
      // Check for error display
      const errorDiv = document.querySelector('#company_name + .text-red-600');
      if (errorDiv) {
        console.log('✓ Error displayed next to field:', errorDiv.textContent);
      } else {
        console.log('✗ No error div found next to field');
      }
      
      // Check for error styling
      const hasErrorStyling = nameInput.classList.contains('border-red-500');
      console.log(hasErrorStyling ? '✓ Field has error styling' : '✗ Field missing error styling');
      
      // Check Vue component errors object
      const vueComponent = nameInput.__vue__ || nameInput.__vueParentComponent?.ctx;
      if (vueComponent && vueComponent.errors) {
        console.log('Vue errors object:', vueComponent.errors);
      }
      
      // Restore original name
      nameInput.value = originalName;
      nameInput.dispatchEvent(new Event('input', { bubbles: true }));
    }, 2000);
  } else {
    console.error('Save button not found');
  }
}

// Run the test when ready
console.log('To test validation, run: testCompanyValidation()');