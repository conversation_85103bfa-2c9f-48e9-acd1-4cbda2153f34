# Fix: Vacation Approval Setting Not Working

## Issue
When creating a new vacation event, it always required approval (status = 'pending') even when the company's `approve_vacations` setting was turned off (unchecked).

## Root Cause
The `Event#set_default_status` method in `app/models/event.rb` was hardcoded to always set vacation events to 'pending' status, ignoring the company's `approve_vacations` setting.

## Solution
Modified the `set_default_status` method to check the company's `approve_vacations` setting:
- If `approve_vacations` is true → vacation status = 'pending'
- If `approve_vacations` is false → vacation status = 'approved'
- Non-vacation events always get 'approved' status regardless of the setting

## Files Changed
1. **app/models/event.rb** - Updated `set_default_status` method to respect company setting
2. **spec/models/event_spec.rb** - Updated tests to verify correct behavior

## How It Works Now
1. When a vacation event is created, the system checks `company.company_setting.approve_vacations`
2. If approval is required (true), the vacation gets 'pending' status
3. If approval is not required (false), the vacation gets 'approved' status automatically
4. Other event types (illness, travel, etc.) always get 'approved' status

## Testing
```ruby
# Test with approve_vacations = false
company.company_setting.update!(approve_vacations: false)
vacation_event = Event.create!(event_type: 'vacation', ...)
vacation_event.status # => 'approved'

# Test with approve_vacations = true  
company.company_setting.update!(approve_vacations: true)
vacation_event = Event.create!(event_type: 'vacation', ...)
vacation_event.status # => 'pending'
```

## Related Settings
- Company settings location: Settings → Company → "Schvalování dovolené" checkbox
- Database field: `company_settings.approve_vacations` (boolean, default: false)