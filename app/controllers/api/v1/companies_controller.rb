# ABOUTME: Handles company listing and switching for multi-tenant context
# ABOUTME: Tenant-agnostic endpoints that allow users to manage their company context

class Api::V1::CompaniesController < Api::V1::ApiController
  
  include ActionPolicy::Controller
  
  # Override parent's tenant validation for these tenant-agnostic actions
  def skip_tenant_validation?
    %w[index switch_company].include?(action_name)
  end
  
  authorize :user, through: :current_user

  # TYM-115: Lightweight company switching endpoint (no JWT regeneration)
  # POST /api/v1/companies/switch
  # Body: { "company_id": 123 }
  # Response: { "success": true, "company": {...} }
  # Client should update X-Company-ID header for subsequent requests
  def switch_company
    company_id = params[:company_id]
    
    if company_id.blank?
      render json: { 
        success: false, 
        error: 'Company ID is required' 
      }, status: :bad_request
      return
    end

    begin
      # Validate user has access to the company
      # If user doesn't have access or company doesn't exist, this will raise ActiveRecord::RecordNotFound
      target_company = current_user.companies.find(company_id)
      
      # Verify user has an active role in the company
      user_company_role = current_user.company_user_roles.active.find_by(company: target_company)
      unless user_company_role
        render json: { 
          success: false, 
          error: 'No active role in this company' 
        }, status: :forbidden
        return
      end
      
      # Update the user's primary company (optional, follows existing pattern)
      current_user.set_primary_company(target_company)
      
      # Log the company switch for monitoring
      Rails.logger.info("[TYM-115] Company switch: User #{current_user.id} switched to company #{target_company.id}")
      
      # Return company info - NO NEW TOKEN GENERATED
      # Client should store company_id locally and use X-Company-ID header
      render json: {
        success: true,
        company: target_company.as_json(
          only: [:id, :name], 
          methods: [:logo_url]
        ),
        role: user_company_role.role.name,
        message: "Successfully switched to #{target_company.name}",
        # Inform client about new architecture
        header_required: true,
        header_name: 'X-Company-ID'
      }, status: :ok
      
    rescue ActiveRecord::RecordNotFound
      # This means the user doesn't have access to the requested company
      render json: { 
        success: false, 
        error: 'Company not found or access denied' 
      }, status: :forbidden
      
    rescue StandardError => e
      # Handle any other errors (database issues, etc.)
      Rails.logger.error("[TYM-115] Company switch error: #{e.message}")
      render json: { 
        success: false, 
        error: 'Internal server error during company switch' 
      }, status: :internal_server_error
    end
  end

  
  # GET /api/v1/companies
  # Returns list of companies user has access to (similar to existing index)
  def index
    # Get current company context before removing tenant scope
    # Check X-Company-ID header first (from frontend company selection)
    current_company_id = request.headers['X-Company-ID']&.to_i
    
    # If no header, fall back to tenant context or user's primary company
    current_company_id ||= ActsAsTenant.current_tenant&.id
    current_company_id ||= current_user.primary_company&.id
    
    ActsAsTenant.without_tenant do
      @company_user_roles = current_user.company_user_roles
                                       .includes(:company, :role)
                                       .where(active: true)  # Only active roles
                                       .order(:created_at) # TODO: Consider ordering by company name or primary status
    end
    
    @current_tenant = current_company_id
    
    # TYM-115 FIX: Include is_primary flag to identify the user's primary company
    # This helps the frontend automatically select the right company context
    render json: {
      company_user_roles: @company_user_roles.map do |role|
        company_data = role.as_json(include: { 
          company: { 
            only: [:id, :name],
            methods: [:logo_url]
          }, 
          role: { methods: [:translated_name] } 
        }).merge(
          is_primary: role.is_primary
        )
        
        # Include subscription information for the company (for Plus plan display)
        subscription = role.company.current_subscription
        if subscription && subscription.plan&.name == 'plus'
          company_data[:subscription] = {
            plan_name: subscription.plan.name,
            start_date: subscription.start_date,
            expire_date: subscription.expire_date
          }
        end
        
        company_data
      end,
      current_tenant: @current_tenant,
      # TYM-115: Include the primary company ID for convenience
      primary_company_id: current_user.primary_company&.id
    }
  end

  private

  # TODO: Integrate Action Policy for explicit authorization checks if needed beyond the find scope
  def authorize_company_access!(company)
    unless current_user.companies.include?(company)
      raise ActionPolicy::Unauthorized, "Access denied to company #{company.id}"
    end
  end
end