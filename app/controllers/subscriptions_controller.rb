class SubscriptionsController < ApplicationController
  
  include ActionPolicy::Controller
  
  before_action :set_tenant_company
  # Authentication handled by ApplicationController's require_login

  def create
    authorize! @company, to: :manage_subscription?
    @plan = Plan.find(params[:plan_id])

    @subscription = @company.subscriptions.build(
      plan: @plan,
      start_date: Date.today,
      expire_date: Date.today + @plan.duration.days,
      status: 'active'
    )

    if @subscription.save
      flash[:notice] = 'Úspěšná registrace!'
      redirect_to root_path
    else
      flash[:alert] = 'Registrace nebyla ú<PERSON>é<PERSON>!'
      redirect_to root_path
    end
  end

  def activate_trial
    authorize! @company, to: :manage_subscription?

    if Subscription.can_activate_trial?(current_user, @company)
      subscription = Subscription.activate_trial(current_user, @company)
      
      if subscription
        render json: { 
          success: true, 
          message: 'Zkušební verze Plus byla úspěšně aktivována.',
          subscription: {
            plan_name: 'plus',
            expire_date: subscription.expire_date
          }
        }
      else
        render json: { 
          success: false, 
          message: 'Nepodař<PERSON> se aktivovat zkušební verzi.',
          messageType: 'error' 
        }, status: :unprocessable_entity
      end
    else
      render json: { 
        success: false, 
        message: '<PERSON><PERSON> jste využili z<PERSON>ní verzi pro tento pracovní prostor.',
        messageType: 'error' 
      }, status: :unprocessable_entity
    end
  end

  def process_request
    # CRITICAL: Must authorize before processing subscription request
    authorize! @company, to: :manage_subscription?
    
    @subscription_request = SubscriptionRequest.new(subscription_request_params)
    
    # Use the authorized tenant company, never trust user input for company lookup
    company = @company
    
    unless company&.name.present? && company&.address.present? && company&.business_id.present?
      render json: { 
        success: false, 
        message: I18n.t('order_request.incomplete_profile', default: 'Pro objednání doplňte údaje firmy')
      }, status: :unprocessable_entity
      return
    end
    
    if @subscription_request.valid?
      # Send notification email to admin
      AdminMailer.subscription_order(@subscription_request, current_user).deliver_now
      
      # Create grace period subscription
      plan = Plan.find_by(name: @subscription_request.tier) || Plan.find_by(name: 'plus')
      
      if company && plan
        # CRITICAL: Wrap in database transaction for atomicity
        ActiveRecord::Base.transaction do
          # Deactivate any existing active subscriptions for this company
          company.subscriptions.where(status: 'active').update_all(status: 'inactive')
          
          # Create new subscription with billing period
          company.subscriptions.create!(
            plan: plan,
            status: 'active',
            payment_status: 'pending_payment',
            start_date: Date.today,
            expire_date: Date.today + (@subscription_request.billing_period.to_i.months),
            grace_period_end: 7.days.from_now
          )
        end
      end
      
      render json: { 
        success: true, 
        message: I18n.t('order_request.sent', default: 'Objednávka byla odeslána')
      }, status: :ok
    else
      render json: { 
        success: false, 
        message: @subscription_request.errors.full_messages.first 
      }, status: :unprocessable_entity
    end
  end

  private

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def subscription_request_params
    params.require(:subscription_request).permit(:company_name, :company_id, :company_address, :email, :custom_message, :tier, :billing_period)
  end

end