<!-- ABOUTME: Simple subscription modal for ordering Plus/Premium tiers -->
<!-- ABOUTME: Uses company data from store, minimal form with billing options -->
<template>
  <div class="subscription-modal">
    <!-- Validation warning if company incomplete -->
    <div v-if="!isCompanyComplete" class="warning-box">
      <p>{{ $t('order_request.incomplete_profile', 'Pro objednání doplňte údaje firmy') }}:</p>
      <ul>
        <li v-if="!company.name">{{ $t('order_request.missing_name', 'Název firmy') }}</li>
        <li v-if="!company.address">{{ $t('order_request.missing_address', 'Adresa') }}</li>
        <li v-if="!company.business_id">{{ $t('order_request.missing_business_id', 'IČO') }}</li>
      </ul>
    </div>

    <!-- Order form -->
    <form v-else @submit.prevent="submitOrder">
      <div class="form-group">
        <label>{{ $t('order_request.billing_period', 'Období platby') }}</label>
        <div class="radio-group">
          <label>
            <input type="radio" v-model="billing_period" value="1">
            {{ $t('order_request.monthly', '1 měsíc') }} {{ $t('pricing.plus.monthly') }} 
          </label>
          <label>
            <input type="radio" v-model="billing_period" value="3">
            {{ $t('order_request.quarterly', '3 měsíce') }} {{ $t('pricing.plus.quarterly') }}
          </label>
          <label>
            <input type="radio" v-model="billing_period" value="6">
            {{ $t('order_request.semiannual', '6 měsíců') }} {{ $t('pricing.plus.semiannual') }}
            <span class="badge">{{ $t('order_request.discount_10', '-10%') }}</span>
          </label>
          <label>
            <input type="radio" v-model="billing_period" value="12">
            {{ $t('order_request.annual', '12 měsíců') }} {{ $t('pricing.plus.annual') }}
            <span class="badge">{{ $t('order_request.discount_20', '-20%') }}</span>
          </label>
        </div>
      </div>

      <div class="form-group">
        <label for="email">{{ $t('order_request.contact_email', 'Kontaktní e-mail') }}</label>
        <input type="email" id="email" v-model="email" required class="form-control">
      </div>

      <div class="form-group">
        <label for="message">{{ $t('order_request.message', 'Poznámka') }}</label>
        <textarea id="message" v-model="message" class="form-control" rows="3"></textarea>
      </div>

      <p class="info-text">
        {{ $t('order_request.grace_period', 'Vaše předplatné bude aktivní po této objednávce. Fakturu zašleme na uvedený e-mail.') }}
      </p>

      <div class="form-actions">
        <button type="button" class="btn btn-outline" @click="$emit('close-modal')">
          {{ $t('cancel', 'Zrušit') }}
        </button>
        <button type="submit" class="btn btn-primary" :disabled="submitting">
          {{ submitting ? $t('sending', 'Odesílání...') : $t('order_request.submit', 'Odeslat objednávku') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  props: {
    tier: {
      type: String,
      required: true
    },
    companyData: {
      type: Object,
      default: () => ({})
    },
    userEmail: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      billing_period: '1',
      email: '',
      message: '',
      submitting: false,
      pricing: {
        plus: { 
          monthly: 270, 
          quarterly: 810, 
          semiannual: 1450,
          annual: 2590
        },
        premium: { 
          monthly: 589, 
          quarterly: 1767,
          semiannual: 3180, 
          annual: 5650
        }
      }
    };
  },
  computed: {
    company() {
      return this.companyData || {};
    },
    isCompanyComplete() {
      return this.company.name && this.company.address && this.company.business_id;
    },
    monthlyPrice() {
      return this.pricing[this.tier]?.monthly || 0;
    },
    quarterlyPrice() {
      return this.pricing[this.tier]?.quarterly || 0;
    },
    semiannualPrice() {
      return this.pricing[this.tier]?.semiannual || 0;
    },
    annualPrice() {
      return this.pricing[this.tier]?.annual || 0;
    }
  },
  mounted() {
    // Pre-fill user email from props
    this.email = this.userEmail || '';
  },
  methods: {
    async submitOrder() {
      this.submitting = true;
      
      try {
        const response = await axios.post('/subscriptions/process_request', {
          subscription_request: {
            tier: this.tier,
            billing_period: this.billing_period,
            company_name: this.company.name,
            company_id: this.company.business_id,
            company_address: this.company.address,
            email: this.email,
            custom_message: this.message
          }
        });
        
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('order_request.success', 'Objednávka byla odeslána'), 
            type: 'success' 
          }
        }));
        
        // Emit success event with subscription data instead of page reload
        this.$emit('order-successful', { 
          subscription: response.data.subscription,
          message: response.data.message 
        });
        this.$emit('close-modal');
      } catch (error) {
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: error.response?.data?.message || this.$t('error', 'Chyba'), 
            type: 'error' 
          }
        }));
      } finally {
        this.submitting = false;
      }
    }
  }
};
</script>

<style scoped>
.subscription-modal {
  padding: 1rem;
}

.warning-box {
  background: #fef3c7;
  border: 1px solid #fde68a;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
}

.warning-box ul {
  margin: 0.5rem 0 0 1.5rem;
  padding: 0;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.radio-group label {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
}

.radio-group label:has(input:checked) {
  border-color: #3b82f6;
  background: #eff6ff;
}

.radio-group input {
  margin-right: 0.5rem;
}

.badge {
  margin-left: 0.5rem;
  padding: 0.125rem 0.5rem;
  background: #10b981;
  color: white;
  font-size: 0.75rem;
  border-radius: 9999px;
}

.info-text {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 1rem 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
}
</style>