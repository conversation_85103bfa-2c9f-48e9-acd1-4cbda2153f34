<!-- ABOUTME: Company index component with inline editing and subscription cards -->
<!-- ABOUTME: Displays companies with badge selector, three-column layout, and proper authorization -->
<template>
  <div class="p-4 md:p-6">
    <!-- Success Message for Company Connection -->
    <div v-if="showSuccessMessage" class="success-message mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
      <div class="flex items-center">
        <div class="p-1 rounded-full bg-green-100 mr-3">
          <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div>
          <h3 class="text-green-800 font-semibold">{{ $t('companies.invitation_accepted', 'Pozvání akceptováno') }}</h3>
          <p class="text-green-700">{{ $t('companies.successfully_connected_to', 'Úspěšně jste se připojili k pracovnímu prostoru') }} <strong>{{ connectedCompanyName }}</strong></p>
        </div>
      </div>
    </div>
    
    <div class="flex justify-between items-center mb-4">
      <div>
        <h2 class="text-xl md:text-2xl font-semibold text-gray-800">{{ $t('companies.workspaces_title', 'Pracovní prostory') }}</h2>
        <p class="text-sm text-gray-500">{{ $t('companies.workspaces_subtitle', 'Firmy a organizace') }}</p>
      </div>
    </div>
    
    <!-- Company Selector with Badge Buttons -->
    <div v-if="companyUserRoles.length > 1" class="mb-6">
      <div class="flex flex-wrap gap-2">
        <button 
          v-for="role in companyUserRoles" 
          :key="role.company.id"
          @click="selectCompany(role.company.id)"
          class="company-selector-badge"
          :class="{
            'company-selector-active': currentViewCompany === role.company.id,
            'company-selector-current': role.company.id === currentTenant
          }"
        >
          {{ role.company.name }}
          <span v-if="role.company.id === currentTenant" class="ml-2 text-xs opacity-75">
            ({{ $t('companies.currently_active', 'Aktivní') }})
          </span>
        </button>
        <button @click="openCreateModal" class="company-selector-badge">
          {{ $t('companies.new_workspace_button', 'Nový pracovní prostor') }}
        </button>
      </div>
      <!-- Company Actions -->
        <hr class="flex flex-row my-4" />
        <div class="gap-2">
          <button 
            v-if="currentViewCompany !== currentTenant"
            @click="switchAssignment(currentViewCompany)" 
            class="btn btn-outline btn-small ml-auto mr-2"
          >
            {{ $t('companies.switch_to_this', 'Přepnout sem') }}
          </button>
          <button 
            @click="confirmLeaveCompany(currentViewCompany, currentCompanyData.name)" 
            class="btn btn-outline btn-danger-light btn-small ml-auto"
          >
            {{ $t('disconnect', 'Odpojit se') }}
          </button>
        </div>
    </div>


    <!-- Main Content - 3 Columns -->
    <div v-if="currentViewCompany && !isLoadingCompany && currentCompanyData" class="grid grid-cols-3">
      
      <!-- Column 1: Company Information -->
      <div>
        <div class="card">
          <div class="card-content p-4">
            <h2 class="text-lg font-semibold mb-4">{{ $t('companies.workspace_information_title', 'Informace') }}</h2>
            
            <!-- Show form only if user can edit -->
            <div v-if="canEditCurrentCompany()">
              <form @submit.prevent="updateCompanyInfo">
                <div class="space-y-3">
                  <div class="form-group">
                    <label for="company_name" class="form-label text-sm">{{ $t('name', 'Název') }}</label>
                    <input 
                      id="company_name" 
                      v-model="currentCompanyData.name" 
                      type="text" 
                      class="form-input" 
                      :class="{ 'border-red-500': errors.name }"
                      required
                    >
                    <div v-if="errors.name" class="text-red-600 text-sm mt-1">
                      {{ errors.name.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label for="company_address" class="form-label text-sm">{{ $t('address', 'Adresa') }}</label>
                    <input 
                      id="company_address" 
                      v-model="currentCompanyData.address" 
                      type="text" 
                      class="form-input"
                      :class="{ 'border-red-500': errors.address }"
                    >
                    <div v-if="errors.address" class="text-red-600 text-sm mt-1">
                      {{ errors.address.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label for="company_business_id" class="form-label text-sm">{{ $t('business_id', 'IČO') }}</label>
                    <input 
                      id="company_business_id" 
                      v-model="currentCompanyData.business_id" 
                      type="text" 
                      class="form-input"
                      :class="{ 'border-red-500': errors.business_id }"
                    >
                    <div v-if="errors.business_id" class="text-red-600 text-sm mt-1">
                      {{ errors.business_id.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label for="company_phone" class="form-label text-sm">{{ $t('phone', 'Telefon') }}</label>
                    <input 
                      id="company_phone" 
                      v-model="currentCompanyData.phone" 
                      type="text" 
                      class="form-input"
                      :class="{ 'border-red-500': errors.phone }"
                    >
                    <div v-if="errors.phone" class="text-red-600 text-sm mt-1">
                      {{ errors.phone.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label for="company_web" class="form-label text-sm">{{ $t('web', 'Web') }}</label>
                    <input 
                      id="company_web" 
                      v-model="currentCompanyData.web" 
                      type="text" 
                      class="form-input"
                      :class="{ 'border-red-500': errors.web }"
                    >
                    <div v-if="errors.web" class="text-red-600 text-sm mt-1">
                      {{ errors.web.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label for="company_description" class="form-label text-sm">{{ $t('description', 'Popis') }}</label>
                    <textarea 
                      id="company_description" 
                      v-model="currentCompanyData.description" 
                      class="form-textarea"
                      :class="{ 'border-red-500': errors.description }"
                      rows="3"
                    ></textarea>
                    <div v-if="errors.description" class="text-red-600 text-sm mt-1">
                      {{ errors.description.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="flex justify-end">
                    <button 
                      type="submit" 
                      class="btn btn-primary btn-small"
                      :disabled="isSavingInfo"
                    >
                      {{ isSavingInfo ? $t('saving', 'Ukládání...') : $t('companies.save_information_button', 'Uložit') }}
                    </button>
                  </div>
                </div>
              </form>
            </div>
            
            <!-- Show read-only text if user cannot edit -->
            <div v-else class="space-y-3">
              <div>
                <span class="text-sm text-gray-500">{{ $t('name', 'Název') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.name || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('address', 'Adresa') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.address || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('business_id', 'IČO') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.business_id || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('phone', 'Telefon') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.phone || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('web', 'Web') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.web || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('description', 'Popis') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.description || '-' }}</p>
              </div>
            </div>
            
            
          </div>
        </div>
      </div>
      
      <!-- Column 2: Company Settings -->
      <div>
        <div class="card">
          <div class="card-content p-4">
            <h2 class="text-lg font-semibold mb-4">{{ $t('settings', 'Nastavení') }}</h2>
            
            <!-- Show settings form only if user can edit -->
            <div v-if="canEditCurrentCompany() && currentCompanySettings">
              <form @submit.prevent="updateCompanySettings">
                <div class="space-y-3">
                  <div class="form-group">
                    <label for="break_duration" class="form-label text-sm">{{ $t('companies.break_duration_label', 'Délka přestávky (minuty)') }}</label>
                    <input 
                      v-model.number="currentCompanySettings.break_duration" 
                      type="number" 
                      id="break_duration" 
                      class="form-input"
                      :class="{ 'border-red-500': errors.break_duration }"
                    >
                    <div v-if="errors.break_duration" class="text-red-600 text-sm mt-1">
                      {{ errors.break_duration.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="form-checkbox-group">
                    <input 
                      v-model="currentCompanySettings.approve_vacations" 
                      type="checkbox" 
                      id="approve_vacations" 
                      class="form-checkbox"
                      :class="{ 'border-red-500': errors.approve_vacations }"
                    >
                    <label for="approve_vacations" class="form-checkbox-label text-sm">
                      {{ $t('companies.approve_vacations_label', 'Schvalování dovolené') }}
                    </label>
                    <div v-if="errors.approve_vacations" class="text-red-600 text-sm mt-1">
                      {{ errors.approve_vacations.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="form-checkbox-group">
                    <input 
                      v-model="currentCompanySettings.daily_team_reports" 
                      type="checkbox" 
                      id="daily_team_reports" 
                      class="form-checkbox"
                      :class="{ 'border-red-500': errors.daily_team_reports }"
                      :disabled="!canReceiveStatusEmails()"
                    >
                    <label 
                      for="daily_team_reports" 
                      class="form-checkbox-label text-sm"
                      :class="{ 'text-gray-400 cursor-not-allowed': !canReceiveStatusEmails() }"
                    >
                      {{ $t('companies.daily_team_reports_label', 'Tým v práci na e-mail') }}
                      <span v-if="!canReceiveStatusEmails()" class="text-xs block">
                        ({{ $t('companies.requires_plus_plan', 'Plus plán') }})
                      </span>
                    </label>
                    <div v-if="errors.daily_team_reports" class="text-red-600 text-sm mt-1">
                      {{ errors.daily_team_reports.join(', ') }}
                    </div>
                  </div>
                  
                  <div class="flex justify-end">
                    <button 
                      type="submit" 
                      class="btn btn-primary btn-small"
                      :disabled="isSavingSettings"
                    >
                      {{ isSavingSettings ? $t('saving', 'Ukládání...') : $t('companies.update_settings_button', 'Aktualizovat') }}
                    </button>
                  </div>
                </div>
              </form>
            </div>
            
            <!-- Show read-only settings if user cannot edit -->
            <div v-else-if="currentCompanySettings" class="space-y-3">
              <div>
                <span class="text-sm text-gray-500">{{ $t('companies.break_duration_label', 'Délka přestávky (minuty)') }}:</span>
                <p class="text-gray-800">{{ currentCompanySettings.break_duration || 30 }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('companies.approve_vacations_label', 'Schvalování dovolené') }}:</span>
                <p class="text-gray-800">
                  {{ currentCompanySettings.approve_vacations ? $t('yes', 'Ano') : $t('no', 'Ne') }}
                </p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('companies.daily_team_reports_label', 'Tým v práci na e-mail') }}:</span>
                <p class="text-gray-800">
                  {{ currentCompanySettings.daily_team_reports ? $t('yes', 'Ano') : $t('no', 'Ne') }}
                  <span v-if="!canReceiveStatusEmails()" class="text-xs text-gray-500">
                    ({{ $t('companies.requires_plus_plan', 'Plus plán') }})
                  </span>
                </p>
              </div>
            </div>
            
            <!-- Message if settings not available -->
            <div v-else class="text-gray-500 text-sm">
              {{ $t('companies.settings_not_available', 'Nastavení není k dispozici') }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Column 3: Subscription/Plan Card -->
      <div v-if="canManageSubscription()">
        <article class="pricing-box" role="region" :aria-labelledby="`planTitle-${currentViewCompany}`">
          <div class="pricing-ribbon" aria-hidden="true"></div>

          <header class="pricing-header">
            <h2 class="text-lg font-semibold" :id="`planTitle-${currentViewCompany}`">
              {{ $t('companies.your_plan', 'Váš plán') }}: {{ getPlanType() }}
            </h2>
            <!-- <div class="pricing-badge" :title="getPlanType() === 'Start' ? 'Doporučeno pro týmy' : 'Váš současný plán'">
              {{ getPlanType() === 'Start' ? 'Doporučeno' : getPlanType() }}
            </div> -->
          </header>

          <p v-if="getPlanType() === 'Start'" class="pricing-subtitle">
            {{ $t('companies.free_tier_message', 'Používáte bezplatnou verzi Start. Upgradujte na Plus pro odemknutí užitečných funkcí, které vám ušetří čas a pomůžou při organizování vaší firmy:') }}
          </p>
          <p v-else-if="hasPlus" class="pricing-subtitle">
            {{ $t('companies.plus_active', 'Plus plán je aktivní') }}
          </p>
          <p v-else class="pricing-subtitle">
            {{ getUpgradeMessage() }}
          </p>
          
          <!-- Validity period for Plus plan -->
          <div v-if="hasPlus && currentSubscription" class="pricing-validity">
            {{ $t('companies.valid_from_to', 'Platnost od') }} {{ formatDate(currentSubscription.start_date) }} {{ $t('companies.to', 'do') }} {{ formatDate(currentSubscription.expire_date) }}
          </div>

          <ul class="pricing-features" :aria-label="`Co získáte v Plánu ${getPlanType() === 'Start' ? 'Plus' : getPlanType()}`">
            <li v-for="feature in getPlanFeatures()" :key="feature" class="pricing-feature">
              <span class="pricing-check" aria-hidden="true">
                <svg viewBox="0 0 24 24" focusable="false" aria-hidden="true">
                  <path d="M20.285 6.709a1 1 0 0 1 0 1.414l-9.9 9.9a1 1 0 0 1-1.414 0l-5.257-5.257a1 1 0 1 1 1.414-1.414l4.55 4.55 9.192-9.193a1 1 0 0 1 1.415 0z"/>
                </svg>
              </span>
              <span>{{ feature }}</span>
            </li>
          </ul>

          <div v-if="getPlanType() === 'Start'" class="pricing-price-row">
            <div class="pricing-price" aria-label="Cena">
              {{ getPriceText() }} <span class="pricing-small">/ měsíc</span>
            </div>
            <div class="pricing-small">Zrušíte kdykoliv</div>
          </div>

          <div class="pricing-cta-wrap">
            <!-- Show upgrade button only for Start plan -->
            <button
              v-if="getPlanType() === 'Start'"
              class="pricing-cta"
              type="button"
              :aria-label="`Upgradovat na Plus za ${getPriceText()} měsíčně`"
              @click="navigateToUpgrade"
            >
              <span class="pricing-cta-row">
                👉 <span>{{ $t('companies.upgrade_to_plus_button', 'Upgradovat na Plus') }}</span>
                <svg class="pricing-arrow" viewBox="0 0 24 24" aria-hidden="true">
                  <path fill="currentColor" d="M13.172 12L8.222 7.05l1.414-1.414L16 12l-6.364 6.364-1.414-1.414z"/>
                </svg>
              </span>
            </button>
            <!-- For Plus plan, leave empty space (no Premium upgrade for now) -->
            <div v-else-if="hasPlus">
              <!-- Empty space - Premium upgrade not available yet -->
            </div>
            <!-- For Premium plan (future) -->
            <div v-else-if="hasPremium" class="pricing-current-plan">
              <span class="pricing-cta-row">
                ✅ <span>{{ $t('companies.current_plan_active', 'Váš současný plán je aktivní') }}</span>
              </span>
            </div>
          </div>
        </article>
      </div>
      <!-- Empty column when user cannot manage subscription -->
      <div v-else>
        <!-- Empty column to maintain 3-column layout -->
      </div>
      
    </div>

    <!-- Loading State -->
    <div v-else-if="isLoadingCompany" class="text-center text-gray-500">
      {{ $t('loading', 'Načítání...') }}
    </div>

    <!-- Create Company Modal (kept for new company creation) -->
    <div v-if="showFormModal && formIsNew" class="modal-overlay">
      <div class="modal-container" style="min-height: auto; max-width: 650px;">
        <div class="modal-header">
          <h3 class="text-lg font-semibold">{{ $t('companies.create_new_workspace_title', 'Vytvořit nový pracovní prostor') }}</h3>
          <button @click="closeFormModal" class="close-btn">&times;</button>
        </div>
        <div class="central-modal-content">
          <CompanyForm 
            :company-id="formCompanyId" 
            :is-new="formIsNew"
            :status-emails="can('can_receive_team_status_emails?')"
            @company-created="handleCompanyCreated"  
            @company-updated="handleCompanyUpdated" 
            @settings-updated="handleSettingsUpdated"
            @logo-updated="handleLogoUpdated" 
            @error="handleError"
          />
        </div>
      </div>
    </div>

    <!-- Leave Company Confirmation Modal -->
    <div v-if="showLeaveConfirm" class="modal-overlay">
       <div class="modal-container" style="min-height: auto; max-width: 500px;">
          <div class="modal-header">
            <h3 class="text-lg font-semibold text-red-600">{{ $t('companies.leave_workspace_warning_title', 'POZOR! Opuštění pracovního prostoru') }}</h3>
             <button @click="cancelLeave" class="close-btn">&times;</button>
          </div>
          <div class="central-modal-content">
            <p class="mb-2">{{ $t('companies.leave_workspace_confirm_text_1', 'Opravdu chcete opustit pracovní prostor') }} <strong>{{ companyToLeave.name }}</strong>?</p>
            <p class="text-sm text-gray-600 mb-4">{{ $t('companies.leave_workspace_confirm_text_2', 'Tato akce je nevratná. Ztratíte přístup ke všem datům v tomto pracovním prostoru.') }}</p>
            <p class="mb-2">{{ $t('companies.enter_to_confirm', 'Pro potvrzení zadejte:') }} <strong class="text-red-700">{{ leaveConfirmationCode }}</strong></p>
            <input 
              v-model="confirmationInput" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 mb-4" 
              :placeholder="$t('companies.enter_confirmation_code_placeholder', 'Zadejte potvrzovací kód')">
             <div class="modal-footer justify-between"> 
              <button @click="cancelLeave" class="btn btn-outline">{{ $t('cancel', 'Zrušit') }}</button>
              <button @click="executeLeave" class="btn btn-danger btn-primary" :disabled="confirmationInput !== leaveConfirmationCode">
                {{ $t('companies.leave_workspace_button', 'Opustit pracovní prostor') }}
              </button>
            </div>
          </div>
       </div>
    </div>

  </div>
</template>

<script>
import authorizationMixin from '../../mixins/authorizationMixin';
import axios from 'axios';
import AuthService from '../../services/authService';
import CompanyForm from './EditCompany.vue';
import CompanyLogo from './CompanyLogo.vue';

export default {
  mixins: [authorizationMixin],
  components: {
    CompanyForm,
    CompanyLogo
  },
  data() {
    return {
      companyUserRoles: [],
      currentTenant: null,
      currentViewCompany: null, // Currently displayed company
      currentCompanyData: null, // Data for displayed company
      currentCompanySettings: null, // Settings for displayed company
      currentSubscription: null, // Subscription data for Plus plan
      isLoadingCompany: false,
      isLoadingSettings: false,
      isSavingInfo: false,
      isSavingSettings: false,
      showLeaveConfirm: false,
      confirmationInput: '',
      leaveConfirmationCode: '',
      companyToLeave: {
        id: null,
        name: ''
      },
      showFormModal: false,
      formCompanyId: null,
      formIsNew: false,
      showSuccessMessage: false,
      connectedCompanyName: '',
      highlightedCompanyId: null,
      successTimeout: null,
      errors: {}
    };
  },
  created() {
    this.fetchCompanyUserRoles();
    this.handleSuccessFlow();
  },
  methods: {
    fetchCompanyUserRoles() {
      // Use JWT-compatible API endpoint with dual authentication support
      // This endpoint supports JWT-first authentication with session fallback and provides
      // enhanced security by filtering to only active company roles
      axios.get('/api/v1/companies', {
        headers: {
          'Accept': 'application/json'
        }
      })
        .then(response => {
          this.companyUserRoles = response.data.company_user_roles;
          this.currentTenant = response.data.current_tenant;
          
          // Set the current view company to the active tenant by default
          if (!this.currentViewCompany && this.currentTenant) {
            this.selectCompany(this.currentTenant);
          }
          
          // After data loads, scroll to highlighted company if needed
          if (this.highlightedCompanyId) {
            this.$nextTick(() => {
              this.scrollToHighlightedCompany();
            });
          }
        })
        .catch(error => {
          console.error('Error fetching company user roles:', error);
          
          // If JWT API fails, we could fallback to the legacy endpoint
          // but this should not be needed since the JWT API supports session fallback
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: this.$t('companies.error_loading_workspaces', 'Nepodařilo se načíst pracovní prostory'), 
              type: 'error' 
            }
          }));
        });
    },
    async switchAssignment(companyId) {
      try {
        // Use AuthService for JWT-first company switching with session fallback
        const result = await AuthService.switchCompany(companyId);
        
        if (result.success) {
          // Update the current tenant in our local state
          this.currentTenant = companyId;
          
          // If using JWT, company data is already updated in the store
          // If using session fallback, reload user data to get updated company context
          if (result.authMethod === 'session') {
            await this.$store.dispatch('userStore/fetchUserData');
          } else if (result.authMethod === 'jwt') {
            // For JWT, we might still want to refresh user data to ensure consistency
            // This is especially important for complex authorization state
            try {
              await this.$store.dispatch('userStore/fetchUserData');
            } catch (error) {
              console.warn('Failed to refresh user data after JWT company switch:', error);
              // Don't fail the company switch for this, the token already contains updated company context
            }
          }
          
          // Show success message
          const successMessage = result.message || this.$t('companies.switched_successfully', 'Pracovní prostor byl přepnut');
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { text: successMessage, type: 'success' }
          }));
          
          // Stay on companies index page instead of redirecting to dashboard
          // This allows user to see the updated company state and switch to other companies if needed
          console.log('[COMPANIES] Company switched successfully, staying on companies index page');
        }
      } catch (error) {
        console.error('Error switching company:', error);
        
        // Extract meaningful error message
        let errorMessage = this.$t('companies.switch_failed', 'Nepodařilo se přepnout pracovní prostor');
        if (error.response?.data?.error) {
          errorMessage = error.response.data.error;
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { text: errorMessage, type: 'error' }
        }));
      }
    },
    selectCompany(companyId) {
      this.currentViewCompany = companyId;
      // Clear any validation errors when switching companies
      this.errors = {};
      
      // Find the user's role in the selected company
      const companyRole = this.companyUserRoles.find(cur => cur.company.id === companyId);
      const userRole = companyRole ? companyRole.role.name : null;
      
      // Update the global company context to match the selected company
      this.$store.commit('userStore/SET_SELECTED_COMPANY', companyId);
      
      // Use cached company data from companyUserRoles (already loaded)
      // This avoids the need for /companies/{id}/edit.json which requires special permissions
      if (companyRole) {
        this.currentCompanyData = companyRole.company;
        // Set subscription data if available (for Plus plan)
        this.currentSubscription = companyRole.subscription || null;
      }
      
      // Check if user has permission to edit/manage the company
      const canEdit = userRole === 'owner' || (userRole === 'admin' && (this.hasPlus || this.hasPremium));
      
      // Only attempt to load full data if:
      // 1. It's the current tenant AND
      // 2. User has edit permissions
      // This avoids authorization errors that trigger logout
      if (companyId === this.currentTenant && canEdit) {
        // For current tenant with edit permissions, we can load full data
        this.loadCompanyData(companyId);
        this.loadCompanySettings(companyId);
      } else if (canEdit) {
        // For non-current tenant with edit permissions, only load settings
        // Skip loadCompanyData as it requires tenant match
        this.isLoadingCompany = false;
        this.loadCompanySettings(companyId);
      } else {
        // For users without edit permissions, just use cached data
        this.isLoadingCompany = false;
        this.currentCompanySettings = null;
        this.isLoadingSettings = false;
      }
    },
    async loadCompanyData(companyId) {
      this.isLoadingCompany = true;
      try {
        const response = await axios.get(`/companies/${companyId}/edit.json`);
        // Merge the detailed data with what we already have
        this.currentCompanyData = { ...this.currentCompanyData, ...response.data.company };
      } catch (error) {
        console.error('Error loading company data:', error);
        // Since we only call this for current tenant, errors are unexpected
        // The cached data from companyUserRoles should still be available
        if (error.response?.status === 403) {
          // User doesn't have edit permissions, but cached data is still valid
          console.log('Using cached company data - edit permissions not available');
        } else {
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: this.$t('companies.error_loading_workspace_data', 'Nepodařilo se načíst data pracovního prostoru'), 
              type: 'error' 
            }
          }));
        }
      } finally {
        this.isLoadingCompany = false;
      }
    },
    async loadCompanySettings(companyId) {
      this.isLoadingSettings = true;
      try {
        let response;
        
        if (companyId === this.currentTenant) {
          // For current tenant, use the standard endpoint
          response = await axios.get('/api/v1/company_settings', {
            headers: { 'Accept': 'application/json' }
          });
        } else {
          // For other companies, use the new endpoint that fetches by company ID
          response = await axios.get(`/api/v1/companies/${companyId}/settings`, {
            headers: { 'Accept': 'application/json' }
          });
        }
        
        this.currentCompanySettings = response.data.company_setting;
      } catch (error) {
        console.error('Error loading company settings:', error);
        
        // Provide specific error messages based on the error type
        let errorMessage;
        if (error.response?.status === 403) {
          errorMessage = this.$t('companies.no_permission_for_settings', 'Nemáte oprávnění zobrazit nastavení tohoto pracovního prostoru');
        } else if (error.response?.status === 404) {
          errorMessage = this.$t('companies.company_not_found', 'Pracovní prostor nebyl nalezen');
        } else {
          errorMessage = this.$t('companies.error_loading_settings_data', 'Nepodařilo se načíst nastavení');
        }
        
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: errorMessage, 
            type: 'error' 
          }
        }));
        // Set to null to hide the card on error.
        this.currentCompanySettings = null;
      } finally {
        this.isLoadingSettings = false;
      }
    },
    async updateCompanyInfo() {
      if (!this.currentViewCompany || !this.currentCompanyData) return;
      
      // Clear previous errors
      this.errors = {};
      this.isSavingInfo = true;
      
      try {
        const response = await axios.put(`/companies/${this.currentViewCompany}.json`, { 
          company: this.currentCompanyData 
        });
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.workspace_updated_success', 'Pracovní prostor byl úspěšně aktualizován'),
            type: 'success' 
          }
        }));
        // Refresh the company list to show updated name
        this.fetchCompanyUserRoles();
      } catch (error) {
        console.error('Error updating company info:', error);
        
        // Handle validation errors specifically
        if (error.response?.status === 422 && error.response?.data?.errors) {
          // Populate field-specific errors (errors is now a hash with field names as keys)
          this.errors = error.response.data.errors;
          
          console.log('Validation errors received:', this.errors); // Debug log
          
          // Show the specific validation message from backend or general message
          const errorMessage = error.response?.data?.message || 
                              this.$t('companies.validation_errors', 'Prosím opravte chyby ve formuláři');
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: errorMessage,
              type: 'error' 
            }
          }));
        } else {
          // Use backend error message if available, otherwise use generic message
          const errorMessage = error.response?.data?.message || 
                              this.$t('companies.error_updating_workspace', 'Nepodařilo se aktualizovat pracovní prostor');
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: errorMessage,
              type: 'error' 
            }
          }));
        }
      } finally {
        this.isSavingInfo = false;
      }
    },
    async updateCompanySettings() {
      if (!this.currentViewCompany || !this.currentCompanySettings) return;
      
      // Only allow settings update for current tenant
      if (this.currentViewCompany !== this.currentTenant) {
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.cannot_update_other_settings', 'Nemůžete aktualizovat nastavení jiného pracovního prostoru'),
            type: 'error' 
          }
        }));
        return;
      }
      
      // Clear previous errors
      this.errors = {};
      this.isSavingSettings = true;
      
      try {
        const settingsToUpdate = {
          break_duration: this.currentCompanySettings.break_duration,
          approve_vacations: this.currentCompanySettings.approve_vacations,
          daily_team_reports: this.currentCompanySettings.daily_team_reports
        };
        
        const response = await axios.patch('/api/v1/company_settings', { 
          company_setting: settingsToUpdate 
        }, {
          headers: { 'Accept': 'application/json' }
        });
        
        // Refresh the company settings store
        this.$store.dispatch('companySettingsStore/fetchSettings', { force: true });
        
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.settings_updated_success', 'Nastavení bylo úspěšně aktualizováno'),
            type: 'success' 
          }
        }));
      } catch (error) {
        console.error('Error updating company settings:', error);
        
        // Handle validation errors specifically
        if (error.response?.status === 422 && error.response?.data?.errors) {
          // Populate field-specific errors (errors is now a hash with field names as keys)
          this.errors = error.response.data.errors;
          
          console.log('Settings validation errors received:', this.errors); // Debug log
          
          // Show the specific validation message from backend or general message
          const errorMessage = error.response?.data?.message || 
                              this.$t('companies.validation_errors', 'Prosím opravte chyby ve formuláři');
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: errorMessage,
              type: 'error' 
            }
          }));
        } else {
          // Use backend error message if available, otherwise use generic message
          const errorMessage = error.response?.data?.message || 
                              this.$t('companies.error_updating_settings', 'Nepodařilo se aktualizovat nastavení');
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: errorMessage,
              type: 'error' 
            }
          }));
        }
      } finally {
        this.isSavingSettings = false;
      }
    },
    canEditCurrentCompany() {
      const role = this.companyUserRoles.find(r => r.company.id === this.currentViewCompany);
      if (!role) return false;
      return this.canEditCompany(role);
    },
    canManageSubscription() {
      // Check if user can manage subscriptions for the current company
      // Based on manage_subscription? policy: owner OR (admin with Plus/Premium plan)
      const role = this.companyUserRoles.find(r => r.company.id === this.currentViewCompany);
      if (!role) return false;
      
      // Owner can always manage subscription
      if (role.role.name === 'owner') return true;
      
      // Admin can manage subscription only with Plus/Premium plan
      if (role.role.name === 'admin' && (this.hasPlus || this.hasPremium)) return true;
      
      // All other roles (supervisor, employee) cannot manage subscriptions
      return false;
    },
    canReceiveStatusEmails() {
      // Check if current company has Plus plan for email features
      // For now, use the can() helper from authorization mixin
      return this.can('can_receive_team_status_emails?');
    },
    // Subscription/Plan helper methods
    getPlanName() {
      // TODO: Get actual plan from company data
      return this.$t('companies.plan_free', 'Free');
    },
    getPlanType() {
      if (this.hasPremium) {
        return 'Premium';
      } else if (this.hasPlus) {
        return 'Plus';
      }
      return 'Start';
    },
    getUpgradeMessage() {
      if (this.hasPlus) {
        return this.$t('companies.plus_active', 'Máte aktivní Plus plán. Můžete upgradovat na Premium.');
      } else if (this.hasPremium) {
        return this.$t('companies.premium_active', 'Máte aktivní Premium plán se všemi funkcemi.');
      }
      return '';
    },
    getPlanFeatures() {
      if (this.hasPlus || this.hasPremium) {
        return [
        ];
      }
      // Features for free tier - what they would get with Plus
      return [
        this.$t('companies.feature_roles', 'Manažérské role uživatelů (zástupce - správce, senior kolega)'),
        this.$t('companies.feature_reports', 'Ranní reporty na e-mail'),
        this.$t('companies.feature_meetings', 'Plánovač schůzek'),
        this.$t('companies.feature_updates', '+ další funkce, ktore pre vas ')
      ];
    },
    canUpgrade() {
      // Can upgrade if not on premium
      return !this.hasPremium;
    },
    getUpgradeButtonText() {
      if (this.hasPlus) {
        return this.$t('companies.upgrade_to_premium_button', 'Upgradovat na Premium');
      }
      return this.$t('companies.upgrade_to_plus_button', 'Upgradovat na Plus');
    },
    getPriceText() {
      const locale = this.$i18n?.locale || 'cs';
      const currency = locale === 'sk' ? '€' : 'Kč';

      if (this.hasPlus) {
        const premiumPrice = locale === 'sk' ? '25' : '589';
        return `${premiumPrice} ${currency}`;
      }
      const price = locale === 'sk' ? '12' : '270';
      return `${price} ${currency}`;
    },
    navigateToUpgrade() {
      // Open subscription modal using CentralModal
      const tier = this.hasPlus ? 'premium' : 'plus';
      document.dispatchEvent(new CustomEvent('open-central-modal', {
        detail: {
          componentName: 'SubscriptionModal',
          title: this.$t('order_request.title', 'Objednávka Týmbox ' + tier.toUpperCase()),
          props: { 
            tier,
            companyData: this.currentCompanyData,
            userEmail: this.$store?.state?.userStore?.currentUser?.email || ''
          }
        }
      }));
    },
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const day = d.getDate().toString().padStart(2, '0');
      const month = (d.getMonth() + 1).toString().padStart(2, '0');
      const year = d.getFullYear();
      return `${day}.${month}.${year}`;
    },
    openCreateModal() {
      this.formCompanyId = null;
      this.formIsNew = true;
      this.showFormModal = true;
    },
    closeFormModal() {
      this.showFormModal = false;
      this.formCompanyId = null;
      this.formIsNew = false;
    },
    handleCompanyCreated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
      this.fetchCompanyUserRoles(); 
      this.closeFormModal();
    },
    handleCompanyUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
      this.fetchCompanyUserRoles(); 
      this.closeFormModal();
    },
    handleSettingsUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
    },
    handleLogoUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
    },
    handleError(errorMessage) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: errorMessage, type: 'error' }
      }));
    },
    confirmLeaveCompany(companyId, companyName) {
      this.companyToLeave = {
        id: companyId,
        name: companyName
      };
      this.leaveConfirmationCode = this.$t('companies.leave_confirmation_code_prefix', 'ODPOJIT-SE-TED-') + companyId;
      this.confirmationInput = '';
      this.showLeaveConfirm = true;
    },
    cancelLeave() {
      this.showLeaveConfirm = false;
      this.confirmationInput = '';
      this.companyToLeave = { id: null, name: '' };
    },
    executeLeave() {
      if (this.confirmationInput !== this.leaveConfirmationCode) return;
      
      axios.post(`/companies/${this.companyToLeave.id}/leave`)
        .then(response => {
          this.showLeaveConfirm = false;
          this.companyUserRoles = this.companyUserRoles.filter(
            role => role.company.id !== this.companyToLeave.id
          );
          
          if (this.companyToLeave.id === this.currentTenant) {
            window.location.href = '/';
          }
        })
        .catch(error => {
          console.error('Error leaving company:', error);
          let message = this.$t('companies.error_leaving_workspace', 'Nepodařilo se opustit pracovní prostor.');
          if (error.response && error.response.data && error.response.data.message) {
            message = error.response.data.message;
          }
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { text: message, type: 'error' }
          }));
        });
    },
    canEditCompany(role) {
      // Only owners can edit companies, or admin/supervisor with advanced roles enabled
      if (role.role.name === 'owner') {
        return true;
      }
      
      // Admin/supervisor can edit only if company has plus/premium plan for advanced roles
      if (role.role.name === 'admin' || role.role.name === 'supervisor') {
        // Check if company has advanced roles enabled (plus/premium plan)
        // This is a simplified check - ideally we'd have this info in the role data
        return false; // For now, be conservative and only allow owners
      }
      
      return false;
    },
    handleSuccessFlow() {
      // Check for success query parameters
      if (this.$route.query.success === 'connected') {
        this.showSuccessMessage = true;
        this.connectedCompanyName = this.$route.query.company || '';
        this.highlightedCompanyId = this.$route.query.highlight || null;
        
        // Auto-dismiss success message after 6 seconds
        this.successTimeout = setTimeout(() => {
          this.showSuccessMessage = false;
        }, 6000);
        
        // Clean up query parameters after a short delay to prevent stale state
        // but allow time for the success message to be displayed
        setTimeout(() => {
          this.$router.replace({
            path: this.$route.path,
            query: {}
          });
        }, 1000);
      }
    },
    isHighlighted(companyId) {
      return this.highlightedCompanyId && this.highlightedCompanyId == companyId;
    },
    scrollToHighlightedCompany() {
      if (!this.highlightedCompanyId) return;
      
      const element = this.$refs[`company-${this.highlightedCompanyId}`];
      if (element && element[0]) {
        element[0].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  },
  beforeDestroy() {
    // Clean up timeout on component destroy
    if (this.successTimeout) {
      clearTimeout(this.successTimeout);
    }
  }
};
</script>

<style scoped>
/* Company selector badges - simple without hover */
.company-selector-badge {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.375rem;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
}

.company-selector-active {
  background-color: #dbeafe;
  border-color: #60a5fa;
  color: #1e40af;
  font-weight: 500;
}

.company-selector-current {
  border-color: #10b981;
}

.success-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure success message is visible on mobile */
@media (max-width: 768px) {
  .success-message {
    margin: 0 -1rem 1.5rem -1rem;
  }
}

/* Pricing Box Styles */
.pricing-box {
  width: 100%;
  max-width: 420px;
  background: #ffffff;
  color: #0f172a;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  padding: 22px;
  position: relative;
  overflow: clip;
  isolation: isolate;
}

.pricing-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 14px;
}

.pricing-title {
  /* font-weight: 800; */
  letter-spacing: -0.02em;
  font-size: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.pricing-spark {
  font-size: 18px;
}

.pricing-badge {
  font-weight: 700;
  font-size: 12px;
  color: #0b1220;
  background: linear-gradient(180deg, #fde68a, #f59e0b);
  padding: 6px 10px;
  border-radius: 0.375rem;
  white-space: nowrap;
}

.pricing-subtitle {
  font-size: 14px;
  color: #475569;
  margin-bottom: 14px;
}

.pricing-validity {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 14px;
  font-style: italic;
}

.pricing-features {
  list-style: none;
  margin: 0 0 18px 0;
  padding: 0;
  display: grid;
  gap: 10px;
}

.pricing-feature {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  font-size: 14px;
}

.pricing-check {
  width: 22px;
  height: 22px;
  border-radius: 8px;
  background: rgba(34,197,94,.15);
  display: grid;
  place-items: center;
  flex: 0 0 22px;
  border: 1px solid rgba(34,197,94,.35);
}

.pricing-check svg {
  width: 14px;
  height: 14px;
  fill: #16a34a;
}

.pricing-price-row {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  gap: 12px;
  margin: 4px 0 18px;
}

.pricing-price {
  font-weight: 800;
  font-size: 28px;
  letter-spacing: -0.02em;
}

.pricing-small {
  color: #475569;
  font-size: 12px;
}

.pricing-cta-wrap {
  display: grid;
  gap: 8px;
}

.pricing-cta {
  appearance: none;
  cursor: pointer;
  border: none;
  outline: none;
  position: relative;
  padding: 14px 18px;
  border-radius: 0.5rem;
  font-weight: 800;
  font-size: 16px;
  color: #06260f;
  background: linear-gradient(92deg, #22c55e, #16a34a 55%, #059669);
  transition: transform .15s ease, filter .15s ease;
}

.pricing-cta:hover {
  transform: translateY(-1px);
  filter: saturate(1.05);
}

.pricing-cta:active {
  transform: translateY(0);
}

.pricing-cta:focus-visible {
  outline: 3px solid rgba(34,197,94,.4);
  outline-offset: 3px;
}

.pricing-cta-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.pricing-arrow {
  width: 18px;
  height: 18px;
}

.pricing-current-plan {
  text-align: center;
  padding: 14px 18px;
  border-radius: 0.5rem;
  background: #f0f9ff;
  border: 2px solid #0ea5e9;
  color: #0c4a6e;
  font-weight: 600;
}

.pricing-current-plan .pricing-cta-row {
  justify-content: center;
}

/* Decorative ribbon */
.pricing-ribbon {
  position: absolute;
  right: -80px;
  top: -80px;
  width: 180px;
  height: 180px;
  background: radial-gradient(80px 80px at 50% 50%, rgba(34,197,94,0.26), transparent 70%);
  transform: rotate(25deg);
  pointer-events: none;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .pricing-box {
    background: #0f172a;
    color: #e2e8f0;
    border-color: #1f2937;
  }

  .pricing-subtitle {
    color: #94a3b8;
  }

  .pricing-small {
    color: #94a3b8;
  }
}
</style>