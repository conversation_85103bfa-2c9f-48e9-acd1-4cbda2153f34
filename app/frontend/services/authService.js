/**
 * Authentication Service - JWT-Only Mode 
 * 
 * Provides JWT-only authentication logic for the single page application.
 * Session authentication fallback has been removed as part of Phase 7 cleanup.
 * 
 * This service acts as the central point for all authentication operations,
 * coordinating between JWT tokens, Redis sessions, and Vuex state management.
 * 
 * Key features:
 * - JWT-only authentication strategy
 * - HttpOnly cookie refresh tokens for security
 * - Redis session storage for page refresh persistence
 * - Automatic token refresh handling
 * - Centralized error handling and logging
 */

import axios from '../utils/axiosSetup';
import store from '../store';
import { sendFlashMessage } from '../utils/flashMessage';
import cable from './cable';

// JWT-Only Mode: Feature flag removed as JWT is now the only authentication method

export default class AuthService {
  /**
   * Log in a user using JWT authentication
   * 
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   * @returns {Promise<Object>} Login result with success status and user data
   */
  static async login(credentials) {
    if (!credentials?.email || !credentials?.password) {
      throw new Error('Email and password are required');
    }

    try {
      // attemptJwtLogin handles token storage & Vuex updates for basic user info.
      // It returns the user object on success or throws an error.
      const { user } = await this.attemptJwtLogin(credentials);
      return {
        success: true,
        authMethod: 'jwt',
        user: user,
        message: 'Logged in successfully via JWT'
      };
    } catch (error) {
      console.error('JWT login failed:', error.message);
      throw new Error('Login failed: ' + (error.response?.data?.error || error.message || 'Please check your credentials and try again.'));
    }
  }

  /**
   * Attempt JWT login
   * @private
   */
  static async attemptJwtLogin(credentials) {
    const response = await axios.post('/api/v1/auth/jwt_login', {
      email: credentials.email,
      password: credentials.password,
      remember_me: credentials.remember_me || false
    }, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success && response.data.access_token) {
      // Store access token only in Vuex memory for XSS protection
      // Refresh token is stored securely in HttpOnly cookie by the backend
      // No frontend refresh token management needed for security reasons

      // Update Vuex store with JWT authentication state
      store.commit('userStore/SET_JWT_TOKEN', response.data.access_token);
      store.commit('userStore/SET_JWT_USER', response.data.user);

      // TYM-115 FIX: Store the primary company ID to establish tenant context
      // This is critical for the X-Company-ID header to be added to subsequent requests
      if (response.data.user && response.data.user.company_id) {
        console.log('[TYM-115] Setting company context after login:', response.data.user.company_id);
        store.commit('userStore/SET_SELECTED_COMPANY', response.data.user.company_id);
      }

      // REMOVED: Call to this.fetchUserDataWithJwt();
      // Rationale: The calling context (e.g., userStore.actions.login) is responsible
      // for fetching detailed user data after successful login. AuthService's primary role
      // is authentication and initial JWT state setup. This avoids redundancy and potential
      // race conditions since userStore.actions.login also calls fetchUserData().

      // Connect to Action Cable with the new JWT token
      try {
        cable.connect();
      } catch (cableError) {
        console.warn('[AuthService] Failed to connect to Action Cable:', cableError);
        // Don't fail the login if cable connection fails
      }

      return {
        success: true,
        user: response.data.user,
        token: response.data.access_token
      };
    }

    throw new Error(response.data.error || 'JWT login failed');
  }


  /**
   * Log out the current user using JWT logout endpoint
   */
  static async logout() {
    const hasJwtToken = store.getters['userStore/hasJwtToken'];
    let jwtLogoutSuccess = true;

    // JWT logout
    if (hasJwtToken) {
      try {
        await axios.post('/api/v1/auth/jwt_logout', {}, {
          headers: {
            'Accept': 'application/json'
          }
        });
      } catch (error) {
        console.warn('JWT logout failed:', error.message);
        jwtLogoutSuccess = false;
        // Continue with cleanup even if server-side logout fails
      }
    }

    // Always clear local authentication state regardless of server response
    this.clearLocalAuthState();

    // Disconnect from Action Cable
    try {
      cable.disconnect();
    } catch (cableError) {
      console.warn('[AuthService] Error disconnecting from Action Cable:', cableError);
      // Don't fail the logout if cable disconnection fails
    }

    // Return status of logout operation
    return {
      success: jwtLogoutSuccess,
      message: 'Logged out successfully'
    };
  }

  /**
   * Clear all local authentication state
   * @private
   */
  static clearLocalAuthState() {
    // Clear JWT tokens
    store.commit('userStore/CLEAR_JWT_TOKEN');
    
    // Note: Refresh tokens are now stored in HttpOnly cookies by the backend
    // They are automatically cleared by the logout endpoint
    // No frontend refresh token management needed for security reasons

    // Clear all authentication state in Vuex
    store.commit('userStore/clearAuth');
  }

  /**
   * Restore JWT token from Redis session during app initialization
   * 
   * @returns {Promise<boolean>} True if token was successfully restored, false otherwise.
   */
  static async restoreSessionToken() {
    try {
      console.log('[DEBUG] AuthService.restoreSessionToken: Attempting to restore JWT token from session');
      
      const response = await axios.post('/api/v1/auth/restore_session.json', {}, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        withCredentials: true, // Required to send session cookies
        _skipRefreshInterceptor: true // Skip interceptor to avoid loops
      });

      if (response.data.success && response.data.access_token) {
        console.log('[DEBUG] AuthService.restoreSessionToken: Successfully restored JWT token');
        
        // Store the restored token in Vuex
        store.commit('userStore/SET_JWT_TOKEN', response.data.access_token);
        
        // TYM-115 FIX: Restore company context from session
        if (response.data.user && response.data.user.company_id) {
          console.log('[TYM-115] Restoring company context from session:', response.data.user.company_id);
          store.commit('userStore/SET_SELECTED_COMPANY', response.data.user.company_id);
        }
        
        return true;
      } else {
        console.log('[DEBUG] AuthService.restoreSessionToken: Failed to restore token');
        return false;
      }
    } catch (error) {
      console.log('[DEBUG] AuthService.restoreSessionToken: Error restoring token:', error.response?.status);
      return false;
    }
  }

  /**
   * Ensures the user is authenticated, refreshing the token if necessary.
   * This is a proactive check to be used at application startup.
   * @returns {Promise<boolean>} True if authentication is successful, false otherwise.
   */
  static async ensureAuthenticated() {
    // First, try to restore JWT token from session if not present
    if (!store.getters['userStore/jwtToken']) {
      console.log('[DEBUG] AuthService.ensureAuthenticated: No JWT token in store, attempting to restore from session');
      const restored = await this.restoreSessionToken();
      if (!restored) {
        console.log('[DEBUG] AuthService.ensureAuthenticated: Failed to restore token from session');
        return false; // No token available
      }
    }

    try {
      // The request interceptor adds the token. The response interceptor handles 401s.
      await store.dispatch('userStore/fetchUserData');
      return store.getters['userStore/isAuthenticated'];
    } catch (error) {
      // If fetchUserData fails (even after a refresh attempt), then auth fails.
      console.error('ensureAuthenticated: Failed to validate and refresh token.', error);
      return false;
    }
  }

  /**
   * Fetch additional user data using JWT authentication
   * @private
   * TODO: (Future Enhancement - Post JWT Core) Consider removing this method
   *       This duplicates functionality with userStore.fetchUserData()
   *       The Axios interceptor handles JWT headers automatically
   *       Single source of truth should be userStore action
   */
  static async fetchUserDataWithJwt() {
    const response = await axios.get('/api/v1/user', {
      headers: {
        'Accept': 'application/json'
      }
    });

    if (response.data) {
      // Update user roles, permissions, and other data
      const userData = response.data;
      
      if (userData.roles) {
        store.commit('userStore/setUserRoles', userData.roles);
      }
      
      if (userData.permissions) {
        store.commit('userStore/setPermissions', userData.permissions);
      }
      
      if (userData.company) {
        store.commit('userStore/setCompany', userData.company);
      }
      
      if (userData.plan_info) {
        store.commit('userStore/setPlanInfo', userData.plan_info);
      }
    }
  }

  /**
   * Refresh the current JWT access token using the refresh token
   * with HttpOnly cookie support and proper error handling
   * 
   * @returns {Promise<boolean>} True if refresh was successful
   */
  static async refreshToken() {
    // DEBUG: Check if this method is being called
    console.log('[DEBUG] AuthService.refreshToken: Method called');
    console.log('[DEBUG] AuthService.refreshToken: axios instance ID:', axios._axiosId);
    
    try {
      console.log('Attempting to refresh JWT access token...');
      
      // Refresh token is automatically sent via HttpOnly cookie
      // No need to manually handle refresh token in frontend
      const response = await axios.post('/api/v1/auth/refresh_token.json', {}, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        // Ensure cookies are sent with the request
        withCredentials: true,
        // Don't trigger refresh interceptor for refresh requests to avoid infinite loops
        _skipRefreshInterceptor: true
      });

      if (response.data.success && response.data.access_token) {
        console.log('JWT token refresh successful');
        
        // Update access token in Vuex memory only for XSS protection
        store.commit('userStore/SET_JWT_TOKEN', response.data.access_token);
        
        // TYM-115 FIX: Update company context if provided in refresh response
        if (response.data.user && response.data.user.company_id) {
          console.log('[TYM-115] Updating company context after token refresh:', response.data.user.company_id);
          store.commit('userStore/SET_SELECTED_COMPANY', response.data.user.company_id);
        }
        
        // Note: New refresh token is automatically stored in HttpOnly cookie by backend
        // No frontend refresh token management needed for security reasons

        // Reconnect Action Cable with the new token
        cable.reconnect().catch(cableError => {
          console.warn('[AuthService] Failed to reconnect Action Cable after token refresh:', cableError);
          // Don't fail the refresh if cable reconnection fails
        });

        return true;
      }

      throw new Error(response.data.error || 'Token refresh failed - invalid response');
    } catch (error) {
      console.error('Token refresh failed:', error.message);
      
      // If the refresh token is invalid or expired, we should logout
      if (error.response?.status === 401 || error.response?.status === 403) {
        console.warn('Refresh token is invalid or expired, clearing local auth state');
        // TODO: (Future Enhancement - Post JWT Core) Consolidate error handling
        //       Both AuthService and Axios interceptor handle auth failures
        //       Consider single source of truth for auth failure handling
        // Clear auth state but don't call logout() to avoid infinite loops
        this.clearLocalAuthState();
        // Flash message and redirection will be handled by the axios interceptor
      }
      
      throw error;
    }
  }

  /**
   * Check if JWT authentication is currently enabled
   * @returns {boolean} Always true in JWT-only mode
   */
  static isJwtEnabled() {
    return true;
  }

  /**
   * Get the current authentication method
   * @returns {string|null} 'jwt', 'session', or null
   */
  static getCurrentAuthMethod() {
    return store.getters['userStore/authMethod'];
  }

  /**
   * Check if user is currently authenticated
   * @returns {boolean} True if authenticated via either method
   */
  static isAuthenticated() {
    return store.getters['userStore/isAuthenticated'];
  }

  /**
   * Get the current user data
   * @returns {Object|null} Current user object or null if not authenticated
   */
  static getCurrentUser() {
    return store.getters['userStore/effectiveUser'];
  }

  /**
   * TYM-115: Switch the user's active company/tenant context (lightweight, no JWT regeneration)
   * 
   * @param {number} companyId - The ID of the company to switch to
   * @returns {Promise<Object>} Switch result with success status and updated company info
   */
  static async switchCompany(companyId) {
    if (!companyId) {
      throw new Error('Company ID is required');
    }

    try {
      const jwtResult = await this.attemptJwtCompanySwitch(companyId);
      if (jwtResult.success) {
        return {
          success: true,
          authMethod: 'jwt',
          company: jwtResult.company,
          message: jwtResult.message,
          role: jwtResult.role
        };
      }
    } catch (error) {
      console.error('Company switch failed:', error.message);
      throw new Error('Company switch failed: ' + error.message);
    }

    throw new Error('Company switch failed');
  }

  /**
   * TYM-115: Attempt lightweight company switching (no JWT regeneration)
   * @private
   */
  static async attemptJwtCompanySwitch(companyId) {
    const response = await axios.post('/api/v1/companies/switch_company', {
      company_id: companyId
    }, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
        // Authorization header is automatically added by Axios interceptor
      }
    });

    if (response.data.success) {
      // TYM-115: No new token - just update local company context
      // Store selected company ID in Vuex and localStorage
      store.commit('userStore/SET_SELECTED_COMPANY', companyId);

      // Update company information in store if provided
      if (response.data.company) {
        store.commit('userStore/setCompany', response.data.company);
      }

      // Reconnect Action Cable with new company context
      // The new X-Company-ID header will be used in subsequent connections
      try {
        cable.reconnect();
      } catch (cableError) {
        console.warn('[TYM-115] Failed to reconnect Action Cable after company switch:', cableError);
        // Don't fail the company switch if cable reconnection fails
      }

      console.log('[TYM-115] Company switched successfully, X-Company-ID will be:', companyId);

      return {
        success: true,
        company: response.data.company,
        role: response.data.role,
        message: response.data.message || 'Company switched successfully'
      };
    }

    throw new Error(response.data.error || 'Company switch failed');
  }


  /**
   * Register a new user account using JWT authentication
   * 
   * This method uses the JWT registration endpoint for complete JWT-only authentication flow.
   * The user is automatically logged in after successful registration with JWT tokens.
   * 
   * @param {Object} userData - Registration data
   * @param {string} userData.email - User email
   * @param {string} userData.password - User password
   * @param {string} userData.password_confirmation - Password confirmation
   * @returns {Promise<Object>} Registration result with JWT tokens and user data
   */
  static async register(userData) {
    // Basic frontend validation
    if (!userData?.email || !userData?.password || !userData?.password_confirmation) {
      throw new Error('Email, password, and password confirmation are required for registration.');
    }

    try {
      // Use JWT registration endpoint - now returns success without access_token 
      // because users must confirm email before logging in
      const response = await axios.post('/api/v1/auth/jwt_register', {
        user: userData
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        // Registration successful - email confirmation required
        return {
          success: true,
          message: response.data.message || 'Registration successful! Please check your email.',
          requiresConfirmation: true,
          email: userData.email
        };
      }

      throw new Error(response.data.error || 'Registration failed to complete successfully.');
    } catch (error) {
      console.error('JWT registration failed:', error.message);
      
      // Handle validation errors specifically
      if (error.response?.status === 422 && error.response?.data?.errors) {
        const errors = error.response.data.errors;
        const errorMessages = Object.entries(errors).map(([field, messages]) => 
          `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`
        ).join('; ');
        throw new Error(`Registration failed: ${errorMessages}`);
      }
      
      throw new Error('Registration failed: ' + (error.response?.data?.error || error.message || 'Please try again.'));
    }
  }

  /**
   * Request a password reset email
   * @param {string} email - User email address
   * @returns {Promise<Object>} Request result with success status
   */
  static async requestPasswordReset(email) {
    try {
      const response = await axios.post('/api/v1/auth/request_password_reset', {
        email: email
      });
      
      return {
        success: true,
        message: response.data.message || 'Password reset instructions have been sent to your email.'
      };
    } catch (error) {
      console.error('[AuthService] Password reset request error:', error);
      
      throw new Error(
        error.response?.data?.error || 
        error.message || 
        'Failed to send password reset instructions'
      );
    }
  }

  /**
   * Reset user password and automatically log them in
   * @param {Object} resetData - Password reset data
   * @param {string} resetData.reset_password_token - Reset token from email
   * @param {string} resetData.password - New password
   * @param {string} resetData.password_confirmation - Password confirmation
   * @returns {Promise<Object>} Reset result with success status
   */
  static async resetPassword(resetData) {
    try {
      // Call password reset API endpoint
      const response = await axios.post('/api/v1/auth/password_reset', resetData);
      
      if (response.data.success) {
        // Password reset successful - user is automatically logged in
        const { access_token, user } = response.data;
        
        // Update Vuex state with user data and JWT (memory-only for XSS protection)
        store.commit('userStore/SET_JWT_TOKEN', access_token);
        
        // CRITICAL FIX: Set company context from response (same as login flow)
        // This was missing, causing 403 errors when fetchUserData tried to use stale localStorage company_id
        if (user && user.company_id) {
          console.log('[AUTH FIX] Setting company context after password reset:', user.company_id);
          store.commit('userStore/SET_SELECTED_COMPANY', user.company_id);
        }
        
        await store.dispatch('userStore/fetchUserData');
        
        console.log('[AuthService] Password reset successful, user logged in');
        
        return {
          success: true,
          message: response.data.message || 'Password reset successful',
          user: user
        };
      } else {
        throw new Error(response.data.error || 'Password reset failed');
      }
    } catch (error) {
      console.error('[AuthService] Password reset error:', error);
      
      // Extract error messages from response
      if (error.response?.data?.messages) {
        return {
          success: false,
          messages: error.response.data.messages
        };
      }
      
      throw new Error(
        error.response?.data?.error || 
        error.message || 
        'An error occurred during password reset'
      );
    }
  }

  /**
   * Confirm user email address and automatically log them in
   * @param {string} confirmationToken - Confirmation token from email
   * @returns {Promise<Object>} Confirmation result with success status
   */
  static async confirmEmail(confirmationToken) {
    try {
      // Call email confirmation API endpoint
      const response = await axios.post('/api/v1/auth/confirm_email', {
        confirmation_token: confirmationToken
      });
      
      if (response.data.success) {
        // Email confirmation successful - user is automatically logged in
        const { access_token, user } = response.data;
        
        // Update Vuex state with user data and JWT (memory-only for XSS protection)
        store.commit('userStore/SET_JWT_TOKEN', access_token);
        
        // TYM-116 FIX: Set company context for new users after email confirmation
        if (user.company_id) {
          console.log('[TYM-116] Setting company context after email confirmation:', user.company_id);
          store.commit('userStore/SET_SELECTED_COMPANY', user.company_id);
        }
        
        // Store companies if provided
        if (user.companies && user.companies.length > 0) {
          console.log('[TYM-116] Setting companies list after email confirmation:', user.companies);
          store.commit('userStore/setCompanies', user.companies.map(c => ({
            id: c.id,
            name: c.name
          })));
        }
        
        await store.dispatch('userStore/fetchUserData');
        
        console.log('[AuthService] Email confirmation successful, user logged in');
        
        return {
          success: true,
          user: user,
          message: response.data.message || 'Email confirmed successfully'
        };
      }
      
      throw new Error(response.data.error || 'Email confirmation failed');
    } catch (error) {
      console.error('[AuthService] Email confirmation error:', error);
      
      throw new Error(
        error.response?.data?.error || 
        error.message || 
        'An error occurred during email confirmation'
      );
    }
  }

  /**
   * Resend email confirmation
   * @param {string} email - User email address
   * @returns {Promise<Object>} Resend result with success status
   */
  static async resendEmailConfirmation(email) {
    try {
      const response = await axios.post('/api/v1/auth/resend_confirmation', {
        email: email
      });
      
      return {
        success: true,
        message: response.data.message || 'Confirmation email sent'
      };
    } catch (error) {
      console.error('[AuthService] Resend confirmation error:', error);
      
      throw new Error(
        error.response?.data?.error || 
        error.message || 
        'Failed to resend confirmation email'
      );
    }
  }

  /**
   * Handle authentication errors and provide user feedback
   * @param {Error} error - The authentication error
   * @param {string} operation - The operation that failed ('login', 'logout', etc.)
   */
  static handleAuthError(error, operation = 'authentication') {
    console.error(`${operation} error:`, error);
    
    // Extract meaningful error message
    let message = 'An authentication error occurred';
    
    if (error.response?.data?.error) {
      message = error.response.data.error;
    } else if (error.message) {
      message = error.message;
    }

    // Show user-friendly flash message
    sendFlashMessage(message, 'error');
    
    return {
      success: false,
      error: message
    };
  }
}