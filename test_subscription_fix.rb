#!/usr/bin/env ruby
# Simple test for subscription form ArgumentError fix

require 'net/http'
require 'json'
require 'uri'

# Test the I18n.t fix by making a request to the subscription endpoint
def test_subscription_endpoint
  puts "Testing subscription form fix..."
  
  # Start the Rails server in test mode
  system("RAILS_ENV=development bundle exec rails server -p 5100 -d")
  sleep 3 # Wait for server to start
  
  begin
    # Test data
    test_data = {
      subscription_request: {
        tier: 'plus',
        billing_period: '1',
        company_name: 'Test Company',
        company_id: 'TC123',
        company_address: 'Test Address',
        email: '<EMAIL>',
        custom_message: 'Test message'
      }
    }
    
    uri = URI('http://0.0.0.0:5100/subscriptions/process_request')
    http = Net::HTTP.new(uri.host, uri.port)
    request = Net::HTTP::Post.new(uri)
    request['Content-Type'] = 'application/json'
    request.body = test_data.to_json
    
    response = http.request(request)
    
    puts "Response status: #{response.code}"
    puts "Response body: #{response.body}"
    
    if response.code == '401'
      puts "✓ Got 401 (authentication required) - this means the ArgumentError is fixed!"
      puts "  (We expect 401 since we're not authenticated, but no 500 error means I18n.t is working)"
    elsif response.code == '500'
      puts "✗ Still getting 500 error - ArgumentError might not be fixed"
    else
      puts "✓ Got #{response.code} - ArgumentError appears to be fixed!"
    end
    
  rescue => e
    puts "Error testing: #{e.message}"
  ensure
    # Stop the server
    system("pkill -f 'rails server'")
  end
end

test_subscription_endpoint