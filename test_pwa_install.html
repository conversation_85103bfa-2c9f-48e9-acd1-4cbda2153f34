<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Install Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>PWA Install Functionality Test</h1>
    
    <div id="status-container"></div>
    
    <button id="install-btn" style="display:none;" onclick="installApp()">
        Install App
    </button>
    
    <h2>Test Results:</h2>
    <ul id="test-results"></ul>

    <script>
        let deferredPrompt = null;
        const statusContainer = document.getElementById('status-container');
        const installBtn = document.getElementById('install-btn');
        const testResults = document.getElementById('test-results');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            statusContainer.appendChild(div);
        }
        
        function addTestResult(test, passed) {
            const li = document.createElement('li');
            li.innerHTML = `${passed ? '✅' : '❌'} ${test}`;
            testResults.appendChild(li);
        }
        
        // Test 1: Check if browser supports PWA
        if ('serviceWorker' in navigator) {
            addTestResult('Service Worker support', true);
        } else {
            addTestResult('Service Worker support', false);
        }
        
        // Test 2: Check if beforeinstallprompt is supported
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            addStatus('✅ PWA can be installed!', 'success');
            addTestResult('beforeinstallprompt event fired', true);
            
            installBtn.style.display = 'block';
            
            // Log the event details
            console.log('beforeinstallprompt event:', e);
        });
        
        // Test 3: Check if already installed
        window.addEventListener('appinstalled', () => {
            addStatus('App was installed!', 'success');
            addTestResult('App installation completed', true);
            installBtn.style.display = 'none';
        });
        
        async function installApp() {
            if (!deferredPrompt) {
                addStatus('No install prompt available', 'warning');
                return;
            }
            
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                addStatus('User accepted the install prompt', 'success');
                addTestResult('User accepted installation', true);
            } else {
                addStatus('User dismissed the install prompt', 'info');
                addTestResult('User dismissed installation', false);
            }
            
            deferredPrompt = null;
            installBtn.style.display = 'none';
        }
        
        // Test 4: Check if running in standalone mode
        if (window.matchMedia('(display-mode: standalone)').matches) {
            addStatus('App is running in standalone mode', 'success');
            addTestResult('Standalone mode detection', true);
        } else {
            addStatus('App is running in browser', 'info');
        }
        
        // Test 5: Check iOS Safari
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        
        if (isIOS && isSafari) {
            addStatus('iOS Safari detected - Manual install required', 'warning');
            addTestResult('iOS Safari detection', true);
        }
        
        // Initial status
        setTimeout(() => {
            if (!deferredPrompt && !window.matchMedia('(display-mode: standalone)').matches) {
                addStatus('⚠️ Install prompt not available. This could mean:', 'warning');
                addStatus('• App is already installed', 'info');
                addStatus('• Browser doesn\'t support PWA installation', 'info');
                addStatus('• PWA criteria not met (HTTPS, manifest, service worker)', 'info');
                addStatus('• User hasn\'t engaged enough with the site', 'info');
            }
        }, 3000);
    </script>
</body>
</html>